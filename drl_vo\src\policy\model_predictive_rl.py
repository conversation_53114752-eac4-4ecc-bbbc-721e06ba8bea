import logging
import torch
import numpy as np
from numpy.linalg import norm
import itertools
from turtlebot_gym.turtlebot_gym.envs.policy.policy import Policy
from turtlebot_gym.turtlebot_gym.envs.utils.action import ActionRot, ActionXY
from turtlebot_gym.turtlebot_gym.envs.utils.state import tensor_to_joint_state
from turtlebot_gym.turtlebot_gym.envs.utils.utils import point_to_segment_dist, point_to_cos
from policy.state_predictor import StatePredictor_maml, LinearStatePredictor, StatePredictor
from policy.graph_model import RGL_maml, RGL
from policy.pcn import PCN_maml, PCN
from policy.value_estimator import ValueEstimator
from shapely.geometry import Polygon # 多边形
import math
import time
import rospy

#计算两个三角形的重合度 
def Cal_area_2poly(data1,data2):
    """
    任意两个图形的相交面积的计算
    :param data1: 当前物体
    :param data2: 待比较的物体
    :return: 当前物体与待比较的物体的面积交集
    '''
    data1 = [] # 带比较的第一个物体的顶点坐标
    data2 = [] #待比较的第二个物体的顶点坐标
    area = Cal_area_2poly(data1,data2)
    '''
    """
 
    poly1 = Polygon(data1).convex_hull # Polygon：多边形对象
    poly2 = Polygon(data2).convex_hull
 
    if not poly1.intersects(poly2):
        inter_area = 0 # 如果两多边形不相交
    else:
        inter_area = poly1.intersection(poly2).area # 相交面积
    return inter_area

from shapely.geometry import Polygon # 多边形
import math
import numpy as np
from numpy.linalg import norm

class ModelPredictiveRL(Policy):
    def __init__(self):
        super().__init__()
        self.name = 'ModelPredictiveRL'
        self.trainable = True
        self.multiagent_training = True
        self.kinematics = None
        self.epsilon = None
        self.gamma = None
        self.sampling = None
        self.speed_samples = None
        self.rotation_samples = None
        self.action_space = None
        self.rotation_constraint = None
        self.speeds = None
        self.rotations = None
        self.action_values = None
        self.robot_state_dim = 9
        self.human_state_dim = 5
        self.v_pref = 1
        self.share_graph_model = None
        self.human_vector_predictor = None
        self.value_estimator = None
        self.linear_state_predictor = None
        self.state_predictor = None
        self.planning_depth = None
        self.planning_width = None
        self.do_action_clip = None
        self.sparse_search = None
        self.sparse_speed_samples = 2
        self.sparse_rotation_samples = 8
        self.action_group_index = []
        self.traj = None
        self.multi_mode = True
        self.maml = True
        self.obstacle = None

    def configure(self, config):
        # rospy.loginfo('Planning depth: {}'.format(self.planning_depth))
        self.set_common_parameters(config)
        self.planning_depth = config.model_predictive_rl.planning_depth
        self.do_action_clip = config.model_predictive_rl.do_action_clip
        if hasattr(config.model_predictive_rl, 'sparse_search'):
            self.sparse_search = config.model_predictive_rl.sparse_search
        self.planning_width = config.model_predictive_rl.planning_width
        self.share_graph_model = config.model_predictive_rl.share_graph_model
        self.linear_state_predictor = config.model_predictive_rl.linear_state_predictor

        if self.linear_state_predictor:
            self.state_predictor = LinearStatePredictor(config, self.time_step)
            graph_model = RGL(config, self.robot_state_dim, self.human_state_dim)
            self.value_estimator = ValueEstimator(config, graph_model)
            self.model = [graph_model, self.value_estimator.value_network]
        else:
            if self.share_graph_model:
                if self.maml:                    
                    graph_model = RGL(config, self.robot_state_dim, self.human_state_dim)
                    self.value_estimator = ValueEstimator(config, graph_model)
                    self.state_predictor = StatePredictor_maml(config, graph_model, self.time_step)
                    self.human_vector_predictor = PCN_maml(latent_dim=64, grid_size=4)
                    # self.model = [graph_model, self.value_estimator.value_network, self.state_predictor.human_motion_predictor,
                    #               self.human_vector_predictor]
                else:
                    graph_model = RGL(config, self.robot_state_dim, self.human_state_dim)
                    self.value_estimator = ValueEstimator(config, graph_model)
                    self.state_predictor = StatePredictor(config, graph_model, self.time_step)
                    self.human_vector_predictor = PCN(latent_dim=64, grid_size=4)
                self.model = [graph_model, self.value_estimator.value_network, self.state_predictor,
                              self.human_vector_predictor]
            else:
                if self.maml:
                    graph_model1 = RGL_maml(config, self.robot_state_dim, self.human_state_dim)
                    self.value_estimator = ValueEstimator(config, graph_model1)
                    graph_model2 = RGL_maml(config, self.robot_state_dim, self.human_state_dim)
                    self.state_predictor = StatePredictor_maml(config, graph_model2, self.time_step)
                    self.human_vector_predictor = PCN_maml(latent_dim=64, grid_size=4)
                    # self.model = [graph_model1, graph_model2, self.value_estimator.value_network,
                    #               self.state_predictor.human_motion_predictor, self.human_vector_predictor]
                else:
                    graph_model1 = RGL(config, self.robot_state_dim, self.human_state_dim)
                    self.value_estimator = ValueEstimator(config, graph_model1)
                    graph_model2 = RGL(config, self.robot_state_dim, self.human_state_dim)
                    self.state_predictor = StatePredictor(config, graph_model2, self.time_step)
                    self.human_vector_predictor = PCN(latent_dim=64, grid_size=4)
                self.model = [graph_model1, graph_model2, self.value_estimator.value_network,
                              self.state_predictor, self.human_vector_predictor]
        # rospy.loginfo('Planning depth: {}'.format(self.planning_depth))
        # rospy.loginfo('Planning width: {}'.format(self.planning_width))
        # rospy.loginfo('Sparse search: {}'.format(self.sparse_search))

        if self.planning_depth > 1 and not self.do_action_clip:
            # rospy.logwarn('Performing d-step planning without action space clipping!')
            print('Performing d-step planning without action space clipping!')

    def set_common_parameters(self, config):
        self.gamma = config.rl.gamma
        self.kinematics = config.action_space.kinematics
        self.sampling = config.action_space.sampling
        self.speed_samples = config.action_space.speed_samples
        self.rotation_samples = config.action_space.rotation_samples
        self.rotation_constraint = config.action_space.rotation_constraint

    def set_device(self, device):
        self.device = device
        for model in self.model:
            model.to(device)

    def set_epsilon(self, epsilon):
        self.epsilon = epsilon

    def set_time_step(self, time_step):
        self.time_step = time_step
        self.state_predictor.time_step = time_step

    def get_normalized_gamma(self):
        return pow(self.gamma, self.time_step * self.v_pref)

    def get_model(self):
        return self.value_estimator

    def get_state_dict(self):
        if self.state_predictor.trainable:
            if self.share_graph_model:

                return {
                    'graph_model': self.value_estimator.graph_model.state_dict(),
                    'value_network': self.value_estimator.value_network.state_dict(),
                    # 'motion_predictor': self.state_predictor.human_motion_predictor.state_dict(),
                    'motion_predictor': self.state_predictor.state_dict(),
                    'human_vector_predictor':self.human_vector_predictor.state_dict()
                }
            else:
                return {
                    'graph_model1': self.value_estimator.graph_model.state_dict(),
                    'graph_model2': self.state_predictor.graph_model.state_dict(),
                    'value_network': self.value_estimator.value_network.state_dict(),
                    # 'motion_predictor': self.state_predictor.human_motion_predictor.state_dict(),
                    'motion_predictor': self.state_predictor.state_dict(),
                    'human_vector_predictor':self.human_vector_predictor.state_dict()
                }
        else:
            return {
                    'graph_model': self.value_estimator.graph_model.state_dict(),
                    'value_network': self.value_estimator.value_network.state_dict(),
                    'human_vector_predictor':self.human_vector_predictor.state_dict()
                }

    def get_traj(self):
        return self.traj

    def load_state_dict(self, state_dict):
        if self.state_predictor.trainable:
            if self.share_graph_model:
                self.value_estimator.graph_model.load_state_dict(state_dict['graph_model'])
            else:
                self.value_estimator.graph_model.load_state_dict(state_dict['graph_model1'])
                self.state_predictor.graph_model.load_state_dict(state_dict['graph_model2'])

            self.value_estimator.value_network.load_state_dict(state_dict['value_network'])
            # self.state_predictor.human_motion_predictor.load_state_dict(state_dict['motion_predictor'])
            self.state_predictor.load_state_dict(state_dict['motion_predictor'])
            self.human_vector_predictor.load_state_dict(state_dict['human_vector_predictor'])
        else:
            self.value_estimator.graph_model.load_state_dict(state_dict['graph_model'])
            self.value_estimator.value_network.load_state_dict(state_dict['value_network'])
        # print("load_state_dict")
    def save_model(self, file):
        torch.save(self.get_state_dict(), file)

    def load_model(self, file):
        checkpoint = torch.load(file)
        self.load_state_dict(checkpoint)

    def build_action_space(self, v_pref):
        """
        Action space consists of 25 uniformly sampled actions in permitted range and 25 randomly sampled actions.
        """
        holonomic = True if self.kinematics == 'holonomic' else False
        # speeds = [(np.exp((i + 1) / self.speed_samples) - 1) / (np.e - 1) * v_pref for i in range(self.speed_samples)]
        speeds = np.linspace(-1, 1, self.speed_samples)
        if holonomic:
            rotations = np.linspace(0, 2 * np.pi, self.rotation_samples, endpoint=False)
        else:
            # rotations = np.linspace(-self.rotation_constraint, self.rotation_constraint, self.rotation_samples)
            rotations = np.linspace(-1, 1, self.rotation_samples)
        action_space = [ActionXY(0, 0) if holonomic else ActionRot(0, 0)]
        for j, speed in enumerate(speeds):
            if j == 0:
                # index for action (0, 0)
                self.action_group_index.append(0)
            # only two groups in speeds
            if j < 3:
                speed_index = 0
            else:
                speed_index = 1

            for i, rotation in enumerate(rotations):
                rotation_index = i // 2

                action_index = speed_index * self.sparse_rotation_samples + rotation_index
                self.action_group_index.append(action_index)

                if holonomic:
                    action_space.append(ActionXY(speed * np.cos(rotation), speed * np.sin(rotation)))
                else:
                    action_space.append(ActionRot(speed, rotation))

        self.speeds = speeds
        self.rotations = rotations
        self.action_space = action_space

    def predict(self, state, obstacle):
        """
        A base class for all methods that takes pairwise joint state as input to value network.
        The input to the value network is always orobot_statef shape (batch_size, # humans, rotated joint state length)

        """
        self.obstacle = obstacle
        if self.phase is None or self.device is None:
            raise AttributeError('Phase, device attributes have to be set!')
        if self.phase == 'train' and self.epsilon is None:
            raise AttributeError('Epsilon attribute has to be set in training phase')

        if self.reach_destination(state):
            if self.kinematics == 'holonomic':
                return 0, ActionXY(0, 0)
            else: 
                return 0, ActionRot(0, 0)
            # return ActionXY(0, 0) if self.kinematics == 'holonomic' else ActionRot(0, 0)
        if self.action_space is None:
            self.build_action_space(state.robot_state.v_pref)

        probability = np.random.random()
        # probability = -10000
        # max_env_match = 0
        if self.phase == 'train' and probability < self.epsilon:
            # print("fd")
            max_action = self.action_space[np.random.choice(len(self.action_space))]
        else:
            # print("fd")
            max_action = None
            max_value = float('-inf')
            # max_env_match = float('-inf')
            max_traj = None

            if self.do_action_clip:
                state_tensor = state.to_tensor(add_batch_size=True, device=self.device)
                action_space_clipped = self.action_clip(state_tensor, self.action_space, self.planning_width)
            else:
                action_space_clipped = self.action_space

            #筛选出距离机器人一定范围内的行人
            # rg = 10
            # na_human = narrow_hum_range(state, rg)

            #print(len(action_space_clipped))
            # print(len(action_space_clipped))
            # t = 0
            # t_1 = 0
            # t_2 = 0
            for action in action_space_clipped:
                
                state_tensor = state.to_tensor(add_batch_size=True, device=self.device)
                # t_1 = t_1 - time.time()
                next_state = self.state_predictor(state_tensor, action)
                # t_1 = t_1 + time.time()
                # t_2 = t_2 - time.time()
                # for next_human in range(next_state[1, 0]):
                #     if norm((next_human[0] - next_state[0, 0], next_human[1] - next_state[0, 1])) < rg:
                max_next_return, max_next_traj = self.V_planning(next_state, self.planning_depth, self.planning_width)
                # t_2 = t_2 + time.time()

                if self.multi_mode:
                    # t = t - time.time()
                    # t = time.time()
                    reward_est = self.estimate_reward_multimode(state, action)
                    # d = time.time()
                    # t = t + time.time()
                else:
                    reward_est = self.estimate_reward(state, action)
                value = reward_est + self.get_normalized_gamma() * max_next_return
                # print("reward:", reward_est, "value:", max_next_return)
                if value > max_value:
                    max_value = value
                    max_action = action
                    max_traj = [(state_tensor, action, reward_est)] + max_next_traj
                    # max_env_match = env_match
                
                # print("t", d-t)
            # print("reward:", t)
            # print("state:", t_1)
            # print("value:", t_2)
            if max_action is None:
                raise ValueError('Value network is not well trained.')

        if self.phase == 'train':
            self.last_state = self.transform(state)
        else:
            self.traj = max_traj

        return max_action

    #这里最后保存的是行人相对于机器人的相对位置
    def narrow_hum_range(self, state, rg):
        na_human = list()
        if isinstance(state, list) or isinstance(state, tuple):
            state = tensor_to_joint_state(state)
        human_states = state.human_states
        robot_state = state.robot_state
        for i, human in enumerate(human_states):
            px = human.px - robot_state.px
            py = human.py - robot_state.py
            dis = px**2 + py**2
            if dis <= rg**2:
                na_human.append([px, py, human.vx, human.vy])
        na_human = np.array(na_human, np.float32)
        # print(na_human)
        return torch.from_numpy(na_human).to("cuda:0")


    def action_clip(self, state, action_space, width, depth=1):
        values = []

        for action in action_space:
            next_state_est = self.state_predictor(state, action)
            next_return, _ = self.V_planning(next_state_est, depth, width)
            reward_est = self.estimate_reward(state, action)
            value = reward_est + self.get_normalized_gamma() * next_return
            values.append(value)

        if self.sparse_search:
            # self.sparse_speed_samples = 2
            # search in a sparse grained action space
            added_groups = set()
            max_indices = np.argsort(np.array(values))[::-1]
            clipped_action_space = []
            for index in max_indices:
                if self.action_group_index[index] not in added_groups:
                    clipped_action_space.append(action_space[index])
                    added_groups.add(self.action_group_index[index])
                    if len(clipped_action_space) == width:
                        break
        else:
            max_indexes = np.argpartition(np.array(values), -width)[-width:]
            clipped_action_space = [action_space[i] for i in max_indexes]

        # print(clipped_action_space)
        return clipped_action_space

    def V_planning(self, state, depth, width):
        """ Plans n steps into future. Computes the value for the current state as well as the trajectories
        defined as a list of (state, action, reward) triples

        """

        current_state_value = self.value_estimator(state)
        if depth == 1:
            return current_state_value, [(state, None, None)]

        if self.do_action_clip:
            action_space_clipped = self.action_clip(state, self.action_space, width)
        else:
            action_space_clipped = self.action_space

        returns = []
        trajs = []

        for action in action_space_clipped:
            next_state_est = self.state_predictor(state, action)
            #reward_est = self.estimate_reward(state, action)
            if self.multi_mode:
                env_match, reward_est = self.estimate_reward_multimode(state, action)
            else:
                reward_est = self.estimate_reward(state, action)
            next_value, next_traj = self.V_planning(next_state_est, depth - 1, self.planning_width)
            return_value = current_state_value / depth + (depth - 1) / depth * (self.get_normalized_gamma() * next_value + reward_est)

            returns.append(return_value)
            trajs.append([(state, action, reward_est)] + next_traj)

        max_index = np.argmax(returns)
        max_return = returns[max_index]
        max_traj = trajs[max_index]

        return max_return, max_traj

    def transform(self, state):
        """
        Take the JointState to tensors

        :param state:
        :return: tensor of shape (# of agent, len(state))
        """
        robot_state_tensor = torch.Tensor([state.robot_state.to_tuple()]).to(self.device)
        human_states_tensor = torch.Tensor([human_state.to_tuple() for human_state in state.human_states]). \
            to(self.device)

        return robot_state_tensor, human_states_tensor

    def estimate_reward_multimode(self, state, action):
        r_arrival = 20 #20 #15
        r_waypoint = 3.2 #3.2 #2.5 #1.6 #2 #3 #1.6 #6 #2.5 #2.5
        r_collision = -20 #-20 #-15
        r_collision_o = -20 #-19.99
        r_scan = -0.6 #-0.2 #-0.15 #-0.3
        r_angle = 0.6 #0.5 #1 #0.8 #1 #0.5
        r_rotation = -0.1 #-0.1 #-0.15 #-0.4 #-0.5 #-0.2 # 0.1
        w_thresh = 1
        r_vector = 0.1
        rg = 3
        
        if self.kinematics == 'nonholonomic':
            vx_min = 0
            vx_max = 0.5
            vz_min = -2 #-3
            vz_max = 2 #3
            v = (action.v + 1) * (vx_max - vx_min) / 2 + vx_min
            r = (action.r + 1) * (vz_max - vz_min) / 2 + vz_min
            real_action = ActionRot(v, r)

        if isinstance(state, list) or isinstance(state, tuple):
            state = tensor_to_joint_state(state)
        human_states = state.human_states
        robot_state = state.robot_state

        na_human = list()
        #判断是否发生碰撞
        dmin = float('inf')
        collision = False
        for i, human in enumerate(human_states):
            if norm((human.px-robot_state.px, human.py-robot_state.py)) > 5:
                continue
            px = human.px - robot_state.px
            py = human.py - robot_state.py
            if self.kinematics == 'holonomic':
                vx = human.vx - real_action.vx
                vy = human.vy - real_action.vy
                ex = px + vx * self.time_step
                ey = py + vy * self.time_step
            else:
                if real_action.r ==0:
                    vx = human.vx - real_action.v * np.cos(robot_state.theta)
                    vy = human.vy - real_action.v * np.sin(robot_state.theta)
                    ex = px + vx * self.time_step
                    ey = py + vy * self.time_step
                else:
                    #错误但是效果好的运动模型
                    vx = human.vx - real_action.v * np.cos(real_action.r + robot_state.theta)
                    vy = human.vy - real_action.v * np.sin(real_action.r + robot_state.theta)
                    ex = px + vx * self.time_step
                    ey = py + vy * self.time_step
                    #正确但是效果不好的运动模型
                    # ex = px + human.vx * self.time_step - real_action.v / real_action.r * (np.sin(robot_state.theta) - np.sin(robot_state.theta + real_action.r * self.time_step)) 
                    # ey = py + human.vy * self.time_step - real_action.v / real_action.r * (np.cos(robot_state.theta + real_action.r * self.time_step) - np.cos(robot_state.theta))
            # closest distance between boundaries of two agents
            rx, ry, closest_dist = point_to_segment_dist(px, py, ex, ey, 0, 0)
            closest_dist = closest_dist - human.radius - robot_state.radius

            #在这里顺便替模式匹配因子收集符合的行人数据
            if closest_dist < rg:
                #px, py是相对位置
                na_human.append([rx, ry, human.vx, human.vy])

            if closest_dist < 0:
                collision = True
                # print("c", closest_dist)
                break
            elif closest_dist < dmin:
                dmin = closest_dist
                # print(dmin)

        if collision:
            reward_c = r_collision
        elif dmin < 2*robot_state.radius:
            reward_c = r_scan * (2*robot_state.radius - dmin)
        else: 
            reward_c = 0

        na_human = np.array(na_human, np.float32)
        na_human = torch.from_numpy(na_human).to("cuda:0")
        
        #另外一种计算是否会和障碍物发生碰撞的方法
        if collision:
            reward_co = 0     
        else:
            dmin = float('inf')
            for obstacle in self.obstacle:
                _,_,dis_0 = point_to_segment_dist(obstacle[0],obstacle[1],obstacle[2],obstacle[3], robot_state.px, robot_state.py)
                dis_0 = dis_0 - robot_state.radius
                if dis_0 > 5:
                    continue
                elif dis_0 < 0:
                    collision = True
                    # print(dis_0)
                    break

                #如果机器人速度为0没有动就不用那么复杂，直接判断当前会不会和周围障碍物发生碰撞即可
                if real_action.v == 0:
                    if dis_0 < dmin:
                        dmin = dis_0
                    continue

                px_0 = robot_state.px - obstacle[0]
                py_0 = robot_state.py - obstacle[1]
                if self.kinematics == 'holonomic':
                    ex_0 = px_0 + real_action.vx * self.time_step
                    ey_0 = py_0 + real_action.vy * self.time_step
                else:
                    ex_0 = px_0 + real_action.v * np.cos(real_action.r + robot_state.theta) * self.time_step
                    ey_0 = py_0 + real_action.v * np.sin(real_action.r + robot_state.theta) * self.time_step
                _, _, closest_dist_0 = point_to_segment_dist(px_0, py_0, ex_0, ey_0, 0, 0)
                closest_dist_0 = closest_dist_0 - robot_state.radius
                if closest_dist_0 < 0:
                    collision = True
                    # print(closest_dist_0)
                    break

                px_1 = robot_state.px - obstacle[2]
                py_1 = robot_state.py - obstacle[3]
                if self.kinematics == 'holonomic':
                    ex_1 = px_1 + real_action.vx * self.time_step
                    ey_1 = py_1 + real_action.vy * self.time_step
                else:
                    ex_1 = px_1 + real_action.v * np.cos(real_action.r + robot_state.theta) * self.time_step
                    ey_1 = py_1 + real_action.v * np.sin(real_action.r + robot_state.theta) * self.time_step
                _, _, closest_dist_1 = point_to_segment_dist(px_1, py_1, ex_1, ey_1, 0, 0)
                closest_dist_1 = closest_dist_1 - robot_state.radius
                if closest_dist_1 < 0:
                    collision = True
                    # print(closest_dist_1)
                    break

                _, _, dis_1 = point_to_segment_dist(ex_0, ey_0, ex_1, ey_1, 0, 0)
                dis_1 = dis_1 - robot_state.radius
                if dis_1 < 0:
                    collision = True
                    # print(dis_1)
                    break
                #计算角度之和,四个顶点分别是(px_0,py_0),(px_1,py_1),(ex_1,ey_1),(ex_0,ey_0)
                # if real_action.v * np.cos(real_action.r + robot_state.theta) == 0:
                #     all_ang = 0
                # else:
                all_ang = point_to_cos(px_0, py_0, px_1, py_1) + point_to_cos(px_1, py_1, ex_1, ey_1) + point_to_cos(ex_1, ey_1, ex_0, ey_0) + point_to_cos(ex_0, ey_0, px_0, py_0)
                
                #
                if int(all_ang*1000000)/ 1000000 == int(2*math.pi*1000000)/ 1000000:
                    collision = True
                    # print()
                    break

                if closest_dist_0 < dmin:
                    dmin = closest_dist_0
                if closest_dist_1 < dmin:
                    dmin = closest_dist_1
                if dis_0 < dmin:
                    dmin = dis_0
                if dis_1 < dmin:
                    dmin = dis_1

            if collision:
                reward_co = r_collision_o
                reward_c = 0
            elif dmin < 2*robot_state.radius:
                reward_co = r_scan * (2*robot_state.radius - dmin)
            else:
                reward_co = 0 
            if reward_co <= reward_c:
                reward_c = 0
            else:
                reward_co = 0

        # check if reaching the goal
        if self.kinematics == 'holonomic':
            px = robot_state.px + real_action.vx * self.time_step
            py = robot_state.py + real_action.vy * self.time_step
        else:
            if real_action.r ==0:
                px = robot_state.px + real_action.v * np.cos(robot_state.theta) * self.time_step
                py = robot_state.py + real_action.v * np.sin(robot_state.theta) * self.time_step
            else:
                #错误但是效果好的运动模型
                theta = robot_state.theta + real_action.r
                px = robot_state.px + np.cos(theta) * real_action.v * self.time_step
                py = robot_state.py + np.sin(theta) * real_action.v * self.time_step
                #正确但是效果不好的运动模型
                # px = robot_state.px + real_action.v / real_action.r * (np.sin(robot_state.theta) - np.sin(robot_state.theta + real_action.r * self.time_step)) 
                # py = robot_state.py + real_action.v / real_action.r * (np.cos(robot_state.theta + real_action.r * self.time_step) - np.cos(robot_state.theta))

        # end_position = np.array((px, py))
        # reaching_goal = norm(end_position - np.array([robot_state.gx, robot_state.gy])) < robot_state.radius

        #计算和目标位置之间距离的奖励
        pre_end_position = np.array((robot_state.px, robot_state.py))
        end_position = np.array((px, py))
        pre_reaching_goal = norm(pre_end_position - np.array([robot_state.gx, robot_state.gy]))
        reaching_goal = norm(end_position - np.array([robot_state.gx, robot_state.gy]))
        
        if reaching_goal < 1:
            reward_g = r_arrival
        else:
            reward_g = r_waypoint*(pre_reaching_goal - reaching_goal)
            # reward_g = 0

        #计算转向角的奖励
        if(abs(real_action.r) > w_thresh):
            reward_w = abs(real_action.r) * r_rotation
        else:
            reward_w = 0.0

        #筛选出距离机器人一定范围内的行人
        
        
        # na_human = self.narrow_hum_range(state, rg)
        '''
        if len(na_human) <= 1:
            reward_v = 0
            # print(len(na_human))
        else:
            # print(len(na_human))
            # a = time.time()
            # print(len(na_human))
            # print(na_human.expand([1,-1,-1]))
            hu_vector = self.human_vector_predictor(na_human.expand([1,-1,-1]))
            # b = time.time()
            #计算当前动作符合运动场的程度,向量相似度+场景密度
            vx = real_action.v * np.cos(real_action.r + robot_state.theta)
            vy = real_action.v * np.sin(real_action.r + robot_state.theta)
            if (hu_vector[0][0] ** 2 + hu_vector[0][1] ** 2) == 0 or (vx ** 2 + vy ** 2) == 0:
                reward_v = 0
            else:
                #这里用余弦相似度计算方向相似性，不考虑大小，大小实际上在碰撞的奖励函数里面就实现了
                reward_v = (hu_vector[0][0] * vx + hu_vector[0][1] * vy) / math.sqrt(hu_vector[0][0] ** 2 + hu_vector[0][1] ** 2) / math.sqrt(vx ** 2 + vy ** 2) - 1
                reward_v = reward_v - 1
                #奖励加上权重引子和环境稀疏程度因子
                # print("match:", reward_v)
                reward_v = reward_v * r_vector * len(na_human) / 30
            # print("match:", len(na_human))
            
            # print("t", b-a)
        '''
        # print(reward_c, reward_co)
        reward = reward_g + reward_w + reward_c + reward_co #+ reward_v 
        
        return reward




        