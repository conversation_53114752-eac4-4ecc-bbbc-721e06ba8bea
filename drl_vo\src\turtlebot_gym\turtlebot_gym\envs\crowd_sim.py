#!/usr/bin/env python
import rospy
from std_msgs.msg import String

import logging
import random
import math
import time
import roslaunch

import gym
import matplotlib.lines as mlines
from matplotlib import patches
import numpy as np
from numpy.linalg import norm
from collections import deque
from .gazebo_connection import GazeboConnection
from gazebo_msgs.msg import ModelStates, ModelState
from gazebo_msgs.srv import GetModelState, SetModelState
from actionlib_msgs.msg import GoalStatusArray

from turtlebot_gym.turtlebot_gym.envs.policy.policy_factory import policy_factory
from turtlebot_gym.turtlebot_gym.envs.utils.state import tensor_to_joint_state, JointState
from turtlebot_gym.turtlebot_gym.envs.utils.action import ActionRot
from turtlebot_gym.turtlebot_gym.envs.utils.human import Human
from turtlebot_gym.turtlebot_gym.envs.utils.info import *
from turtlebot_gym.turtlebot_gym.envs.utils.utils import point_to_segment_dist

from pedsim_msgs.msg import AgentState
from pedsim_msgs.msg import AgentStates
from pedsim_msgs.msg import TrackedPersons
from pedsim_msgs.msg import LineObstacle, LineObstacles
from geometry_msgs.msg import Point, PoseStamped
from geometry_msgs.msg import Pose, Twist, PoseWithCovarianceStamped

from cnn_msgs.msg import CNN_data
from nav_msgs.msg import Odometry, OccupancyGrid

import time
import torch

#机器人的起始位置
initial_pos_a = np.array([[[35.5, -1, -0.57], [34, 8, 0.5233], [34, 18, -1.57], [6, 4, 0], [10, 8, 2.933], [9, 1, 1.57], [14, 5, 0.342], [19, 4.5, 3.14], [19, 4.5, 0], [21, 5, 0.33], [27, 2.5, 1.75], [30, 10.5, 1.35], [33, -2.5, -1.45], [36, 11, -0.367], [44, 10, 3.14], [31, 2, -0.254], [27, 8.5, 0.189], [18, 7.5, -1.57], [44, 1.5, 3.14]]])
initial_pos_t = np.array([[[0, -0.5, 1.8], [6.5, 0, 0], [0, 8.5, -1.23], [-1.5, 11, 0.95], [4, 15, -0.52], [8, 11.5, 1.57], [6.5, 15, -0.85], [13, 14.5, 0], [16.5, 15.5, -1.17], [20.5, 15, -1.08], [26.5, 9.5, 2.39], [27, 7, -1.09], [29.5, 2.5, -1.23], [32, 1, 1.57], [30.5, -1.5, 1], [28, -3, 1.57], [20, 0, 0.95], [14, 2.5, 3.44], [10.5, -1, 2.92]]])
initial_pos_a = np.concatenate((initial_pos_a, initial_pos_t), axis=0)
initial_pos_t = np.array([[[1, 1, 0], [14, 7, 1.5705], [1, 16, 0], [14, 22.5, -1.3113], [4, 4, 1.5705], [2, 9, 0], [30, 9, 3.14], [25, 17, 3.14], [5, 8, 0], [10, 12, 0], [14, 15, 1.576], [18.5, 15.7, 3.14], [18.5, 11.3, 3.14], [14, 11.3, 3.14], [12.5, 13.2, 0.78], [12.07, 16.06, 0], [21, 14, -1.576], [14, 22.5, 1.576], [18, 12, -1.576]]])
initial_pos_a = np.concatenate((initial_pos_a, initial_pos_t), axis=0)

class CrowdSim(gym.Env):
    metadata = {'render.modes': ['human']}

    def __init__(self):
        """
        Movement simulation for n+1 agents
        Agent can either be human or robot.
        humans are controlled by a unknown and fixed policy.
        robot is controlled by a known and learnable policy.

        """
        self.lsy_code = True

        self.time_limit = None
        self.time_step = None
        self.robot = None
        self.humans = None
        self.global_time = None
        self.robot_sensor_range = None
        # reward function
        self.success_reward = None
        self.collision_penalty = None
        self.discomfort_dist = None
        self.discomfort_penalty_factor = None
        # simulation configuration
        self.config = None
        self.case_capacity = None
        self.case_size = None
        self.case_counter = None
        self.randomize_attributes = None
        self.train_val_scenario = None
        self.test_scenario = None
        self.current_scenario = None
        self.square_width = None
        self.circle_radius = None
        self.human_num = None
        self.all_humans = None
        self.nonstop_human = None
        self.centralized_planning = None
        self.centralized_planner = None

        # for visualization
        self.states = None
        self.action_values = None
        self.attention_weights = None
        self.robot_actions = None
        self.rewards = None
        self.As = None
        self.Xs = None
        self.feats = None
        self.trajs = list()
        self.panel_width = 10
        self.panel_height = 10
        self.panel_scale = 1
        self.test_scene_seeds = []
        self.dynamic_human_num = []
        self.human_starts = []
        self.human_goals = []

        self.phase = None

        self.seed()

        # robot parameters:
        self.ROBOT_RADIUS = 0.3
        self.GOAL_RADIUS = 1 #0.3
        self.DIST_NUM = 10
        self.pos_valid_flag = True
        # bumper:
        self.bump_flag = False
        self.bump_num = 0
        # reward:
        self.dist_to_goal_reg = np.zeros(self.DIST_NUM)
        self.num_iterations = 0

        # action limits
        self.max_linear_speed = 0.5
        self.max_angular_speed = 2
        # observation limits
        # action space
        # self.high_action = np.array([self.max_linear_speed, self.max_angular_speed])
        # self.low_action = np.array([0, -self.max_angular_speed])
        # MaxAbsScaler: normalize to (-1,1)
        self.high_action = np.array([1, 1])
        self.low_action = np.array([-1, -1])
        # self.action_space = spaces.Box(low=self.low_action, high=self.high_action, dtype=np.float32)
        # observation space
        self.cnn_data = CNN_data()
        self.ped_pos = []
        self.scan = []
        self.goal = []
        #self.vel = []
       
        # self.observation_space = spaces.Box(low=-30, high=30, shape=(6402,), dtype=np.float32)
        # MaxAbsScaler: normalize to (-1,1)
        # self.observation_space = spaces.Box(low=-1, high=1, shape=(19202,), dtype=np.float32)
        # info, initial position and goal position
        self.init_pose = Pose()
        self.curr_pose = Pose()
        self.curr_vel = Twist()
        self.goal_position = Point()
        self.info = {}
        # episode done flag:
        self._episode_done = False
        # goal reached flag:
        self._goal_reached = False
        # reset flag:
        self._reset = True

        # vo algorithm:
        self.mht_peds = TrackedPersons()
        self.obs_msg = LineObstacles()
        self.obstacles = None
        # self.t = rospy.Time.now()
        # self.t_e = rospy.Time.now()
        # self.t_s = rospy.Time.now()

        self.tracking_launch = None
        self.task_num = None

        self.start_simulation(0)

        self.gazebo = GazeboConnection(
        start_init_physics_parameters=True,
        reset_world_or_sim= "WORLD" #"SIMULATION" #
        )

        self.gazebo.unpauseSim()

        self._map_sub = rospy.Subscriber("/map", OccupancyGrid, self._map_callback)
        self._cnn_data_sub = rospy.Subscriber("/cnn_data", CNN_data, self._cnn_data_callback, queue_size=1, buff_size=2**24)
        self._robot_pos_sub = rospy.Subscriber("/robot_pose", PoseStamped, self._robot_pose_callback) #, queue_size=1)
        self._robot_vel_sub = rospy.Subscriber('/odom', Odometry, self._robot_vel_callback) #, queue_size=1)
        self._final_goal_sub = rospy.Subscriber("/move_base/current_goal", PoseStamped, self._final_goal_callback) #, queue_size=1)
        self._goal_status_sub = rospy.Subscriber("/move_base/status", GoalStatusArray, self._goal_status_callback) #, queue_size=1)
        self._ped_sub = rospy.Subscriber("/track_ped", TrackedPersons, self._ped_callback)
        self._orig_ped_sub = rospy.Subscriber('/pedsim_visualizer/tracked_persons', TrackedPersons, self._orig_ped_callback)
        self._obs_sub = rospy.Subscriber('/pedsim_simulator/simulated_walls', LineObstacles, self._obs_callback)
        self._cmd_vel_pub = rospy.Publisher('/drl_cmd_vel', Twist, queue_size=5, latch=False)
        self._initial_pose_pub = rospy.Publisher('/initialpose', PoseWithCovarianceStamped, queue_size=1, latch=True)
        self._initial_goal_pub = rospy.Publisher('/move_base_simple/goal', PoseStamped, queue_size=1, latch=True)
        self._set_robot_state_service = rospy.ServiceProxy('/gazebo/set_model_state', SetModelState)

        self._check_all_systems_ready()
        self.gazebo.pauseSim() 

    def start_simulation(self, task_num):
        self.task_num = task_num
        uuid = roslaunch.rlutil.get_or_generate_uuid(None, False)
        roslaunch.configure_logging(uuid)
        if task_num == 0:
            self.tracking_launch = roslaunch.parent.ROSLaunchParent(uuid, ["/home/<USER>/catkin_wd/src/drl_vo_nav/drl_vo/launch/mode1.launch"])
        elif task_num == 1:
            self.tracking_launch = roslaunch.parent.ROSLaunchParent(uuid, ["/home/<USER>/catkin_wd/src/drl_vo_nav/drl_vo/launch/mode2.launch"])
        elif task_num == 8:
            self.tracking_launch = roslaunch.parent.ROSLaunchParent(uuid, ["/home/<USER>/catkin_wd/src/drl_vo_nav/drl_vo/launch/mode_test.launch"])
        self.tracking_launch.start()

    def shut_simulation(self):
        self.tracking_launch.shutdown() 

    def _check_all_systems_ready(self):
        """
        Checks that all the subscribers, publishers and other simulation systems are
        operational.
        """
        # self.gazebo.unpauseSim()
        self._check_all_subscribers_ready()
        self._check_all_publishers_ready()
        # self.gazebo.pauseSim()
        return True

    # check subscribers ready:
    def _check_all_subscribers_ready(self):
        rospy.logdebug("START TO CHECK ALL SUBSCRIBERS READY")
        self._check_subscriber_ready("/map", OccupancyGrid)
        self._check_subscriber_ready("/cnn_data", CNN_data)
        self._check_subscriber_ready("/robot_pose", PoseStamped)
        #self._check_subscriber_ready("/mobile_base/commands/velocity", Twist)
        #self._check_subscriber_ready("/move_base/current_goal", PoseStamped)
        #self._check_subscriber_ready("/move_base/status", GoalStatusArray)
        #self._check_subscriber_ready("/gazebo/model_states", ModelStates)
        #self._check_subscriber_ready("/mobile_base/events/bumper", BumperEvent)
        rospy.logdebug("ALL SUBSCRIBERS READY")

    def _check_subscriber_ready(self, name, type, timeout=5.0):
        """
        Waits for a sensor topic to get ready for connection
        """
        var = None
        rospy.logdebug("Waiting for '%s' to be READY...", name)        
        while var is None and not rospy.is_shutdown():
            try:
                var = rospy.wait_for_message(name, type, timeout)
                rospy.logdebug("Current '%s' READY=>", name)
            except:
                rospy.logfatal('Sensor topic "%s" is not available. Waiting...', name)
        return var
    
    # check publishers ready:
    def _check_all_publishers_ready(self):
        rospy.logdebug("START TO CHECK ALL PUBLISHERS READY")
        self._check_publisher_ready(self._cmd_vel_pub.name, self._cmd_vel_pub)
        self._check_publisher_ready(self._initial_goal_pub.name, self._initial_goal_pub)
        #self._check_publisher_ready(self._set_robot_state_publisher.name, self._set_robot_state_publisher)
        self._check_service_ready('/gazebo/set_model_state')
        # self._check_publisher_ready(self._reset_odom_pub.name, self._reset_odom_pub)
        self._check_publisher_ready(self._initial_pose_pub.name, self._initial_pose_pub)
        rospy.logdebug("ALL PUBLISHERS READY")

    def _check_publisher_ready(self, name, obj, timeout=5.0):
        """
        Waits for a publisher to get response
        """
        rospy.logdebug("Waiting for '%s' to get response...", name) 
        start_time = rospy.Time.now()
        while obj.get_num_connections() == 0 and not rospy.is_shutdown():
                rospy.logfatal('No subscriber found for publisher %s. Exiting', name)
        rospy.logdebug("'%s' Publisher Connected", name)

    def _check_service_ready(self, name, timeout=5.0):
        """
        Waits for a service to get ready
        """
        rospy.logdebug("Waiting for '%s' to be READY...", name)
        try:
            rospy.wait_for_service(name, timeout)
            rospy.logdebug("Current '%s' READY=>", name)
        except (rospy.ServiceException, rospy.ROSException):
            rospy.logfatal("Service '%s' unavailable.", name)  

    # Call back functions read subscribed sensors' data
    # ----------------------------
    # Callback function for the map subscriber
    def _map_callback(self, map_msg):
        """
        Receiving map from map topic
        :param: map data
        :return:
        """
        self.map = map_msg

    # Callback function for the cnn_data subscriber
    def _cnn_data_callback(self, cnn_data_msg):
        """
        Receiving cnn data from cnn_data topic
        :param: cnn data
        :return:
        """
        self.cnn_data = cnn_data_msg

    # Callback function for the robot pose subscriber
    def _robot_pose_callback(self, robot_pose_msg):
        """
        Receiving robot pose from robot_pose topic
        :param: robot pose
        :return:
        """
        self.curr_pose = robot_pose_msg.pose
        self.curr_pose.position.x = self.curr_pose.position.x + 1
        self.curr_pose.position.y = self.curr_pose.position.y + 1
    
    # Callback function for the robot velocity subscriber
    def _robot_vel_callback(self, robot_vel_msg):
        """
        Receiving robot velocity from robot_vel topic
        :param: robot velocity
        :return:
        """
        self.curr_vel = robot_vel_msg.twist.twist

    def _final_goal_callback(self, final_goal_msg):
        """
        Receiving final goal from final_goal topic
        :param: final goal
        :return:
        """
        self.goal_position = final_goal_msg.pose.position


    def _goal_status_callback(self, goal_status_msg):
        """
        Checking goal status callback from global planner.
        """
        if(len(goal_status_msg.status_list) > 0):
            last_element = goal_status_msg.status_list[-1]
            rospy.logwarn(last_element.text)
            if(last_element.status == 3): # succeeded 
                self._goal_reached = True
            else:
                self._goal_reached = False  
        else:
            self._goal_reached = False       
        return

    def _ped_callback(self, trackPed_msg): 
        self.mht_peds_r = trackPed_msg

    def _orig_ped_callback(self, trackPed_msg): 
        self.mht_peds = trackPed_msg

    def _obs_callback(self, obs_msg):
        self.obstacles = list()
        self.obs_msg = obs_msg
        for line_obstacle in self.obs_msg.obstacles:
            self.obstacles.append([line_obstacle.start.x, line_obstacle.start.y, line_obstacle.end.x, line_obstacle.end.y])
        # print(self.obstacles)
        #这里验证了是对的和xml文件里面是完全对应的

    def _take_action(self, action):
        self.t_s = rospy.Time.now()
        """
        Set linear and angular speed for Turtlebot and execute.
        Args:
        action: 2-d numpy array.
        """
        # rospy.logdebug("TurtleBot2 Base Twist Cmd>>\nlinear: {}\nangular: {}".format(action[0], action[1]))
        cmd_vel = Twist()
        # distance to goal:
        #distance = np.linalg.norm(self.goal)
        #if(distance > 0.7):
        # MaxAbsScaler:
        vx_min = 0
        vx_max = 0.5
        vz_min = -2 #-3
        vz_max = 2 #3
        # action原本的取值范围应该是[-1,1]
        cmd_vel.linear.x = (action[0] + 1) * (vx_max - vx_min) / 2 + vx_min
        cmd_vel.angular.z = (action[1] + 1) * (vz_max - vz_min) / 2 + vz_min
        # cmd_vel.linear.x = 0.5
        # cmd_vel.angular.z = 1.57
        # cmd_vel.linear.x = action[0]
        # cmd_vel.angular.z = action[1]
        #print("[",action[0],",",action[1],"]")
        #self._check_publishers_connection()
    
        rate = rospy.Rate(1)
        # self.t = rospy.Time.now()
        for _ in range(1):
            self._cmd_vel_pub.publish(cmd_vel)
            #rospy.logdebug("cmd_vel: \nlinear: {}\nangular: {}".format(cmd_vel.linear.x,
            #                                                    cmd_vel.angular.z))
            rate.sleep()
            #time.sleep(0.05)
            # t = rospy.Time.now()
            # print("t", (t - self.t))
        # self.t = t
        # self._cmd_vel_pub.publish(cmd_vel)
        # rospy.logwarn("cmd_vel: \nlinear: {}\nangular: {}".format(cmd_vel.linear.x, cmd_vel.angular.z))
        # self.t_e = rospy.Time.now()



    def configure(self, config, human_vector_predictor):
        self.human_vector_predictor = human_vector_predictor
        self.config = config
        self.time_limit = config.env.time_limit
        self.time_step = config.env.time_step
        self.randomize_attributes = config.env.randomize_attributes
        self.robot_sensor_range = config.env.robot_sensor_range
        self.success_reward = config.reward.success_reward
        self.collision_penalty = config.reward.collision_penalty
        self.discomfort_dist = config.reward.discomfort_dist
        self.discomfort_penalty_factor = config.reward.discomfort_penalty_factor
        self.case_capacity = {'train': np.iinfo(np.uint32).max - 2000, 'val': 1000, 'test': 1000}
        self.case_size = {'train': config.env.train_size, 'val': config.env.val_size,
                          'test': config.env.test_size}
        self.train_val_scenario = config.sim.train_val_scenario
        self.test_scenario = config.sim.test_scenario
        self.square_width = config.sim.square_width
        self.circle_radius = config.sim.circle_radius
        self.human_num = config.sim.human_num

        self.nonstop_human = config.sim.nonstop_human
        self.centralized_planning = config.sim.centralized_planning
        self.case_counter = {'train': 0, 'test': 0, 'val': 0}

        human_policy = config.humans.policy
        if self.centralized_planning:
            if human_policy == 'socialforce':
                # logging.warning('Current socialforce policy only works in decentralized way with visible robot!')
                print('Current socialforce policy only works in decentralized way with visible robot!')
            self.centralized_planner = policy_factory['centralized_' + human_policy]()

        # logging.info('human number: {}'.format(self.human_num))
        if self.randomize_attributes:
            rospy.loginfo("Randomize human's radius and preferred speed")
        else:
            rospy.loginfo("Not randomize human's radius and preferred speed")
        rospy.loginfo('Training simulation: {}, test simulation: {}'.format(self.train_val_scenario, self.test_scenario))
        rospy.loginfo('Square width: {}, circle width: {}'.format(self.square_width, self.circle_radius))

    def generate_all_humans(self):

        # while len(self.q_people_) < 1:
        #     agents = rospy.wait_for_message("/pedsim_simulator/simulated_agents", AgentStates, timeout=None)
        #     self.q_people_.appendleft(agents)

        self.all_humans = []

        # current_states = self.q_people_[0]
        # human = Human(self.config, 'humans')
        for agent_state in self.mht_peds.tracks:
            # if agent_state.type == 2:
            #     continue
            human = Human(self.config, 'humans')
            px = agent_state.pose.pose.position.x
            py = agent_state.pose.pose.position.y
            vx = agent_state.twist.twist.linear.x
            vy = agent_state.twist.twist.linear.y
            #只考虑距离机器人一定范围里面的行人
            # if norm((px-self.robot.px, py-self.robot.py)) > 7:
            #     continue
            h_id = agent_state.track_id
            # print("[",px,py,vx,vy,"]")
            theta = math.atan2(vy, vx)
            human.set(px, py, 0, 0, vx, vy, theta)
            human.set_id(h_id)
            self.all_humans.append(human)
        #     print("[",self.all_humans[-1].px, self.all_humans[-1].py, self.all_humans[-1].vx,self.all_humans[-1].vy,"]")
        # print("---------------------------------------------------------next")
        # for human in self.all_humans:
        #     print("[",human.px, human.py, human.vx,human.vy,"]")

    def set_robot(self, robot):
        self.robot = robot

    def generate_human(self, human=None):
        if human is None:
            human = Human(self.config, 'humans')
        if self.randomize_attributes:
            human.sample_random_attributes()

        if self.current_scenario == 'circle_crossing':
            while True:
                angle = np.random.random() * np.pi * 2
                # add some noise to simulate all the possible cases robot could meet with human
                px_noise = (np.random.random() - 0.5) * human.v_pref
                py_noise = (np.random.random() - 0.5) * human.v_pref
                px = self.circle_radius * np.cos(angle) + px_noise
                py = self.circle_radius * np.sin(angle) + py_noise
                collide = False
                for agent in [self.robot] + self.humans:
                    min_dist = human.radius + agent.radius + self.discomfort_dist
                    if norm((px - agent.px, py - agent.py)) < min_dist or \
                            norm((px - agent.gx, py - agent.gy)) < min_dist:
                        collide = True
                        break
                if not collide:
                    break
            human.set(px, py, -px, -py, 0, 0, 0)

        elif self.current_scenario == 'square_crossing':
            if np.random.random() > 0.5:
                sign = -1
            else:
                sign = 1
            while True:
                px = np.random.random() * self.square_width * 0.5 * sign
                py = (np.random.random() - 0.5) * self.square_width
                collide = False
                for agent in [self.robot] + self.humans:
                    if norm((px - agent.px, py - agent.py)) < human.radius + agent.radius + self.discomfort_dist:
                        collide = True
                        break
                if not collide:
                    break
            while True:
                gx = np.random.random() * self.square_width * 0.5 * - sign
                gy = (np.random.random() - 0.5) * self.square_width
                collide = False
                for agent in [self.robot] + self.humans:
                    if norm((gx - agent.gx, gy - agent.gy)) < human.radius + agent.radius + self.discomfort_dist:
                        collide = True
                        break
                if not collide:
                    break
            human.set(px, py, gx, gy, 0, 0, 0)

        return human

    def _reset_sim(self):
        """Resets a simulation
        """
        rospy.logdebug("START robot gazebo _reset_sim")
        self.gazebo.unpauseSim()
        # self.gazebo.resetSim()
        # rospy.loginfo("reset_sim")
        self._set_init()
        self.gazebo.pauseSim()
        rospy.logdebug("END robot gazebo _reset_sim")
        return True

    def _set_init(self):
        """ 
        Set initial condition for simulation
        1. Set turtlebot at a random pose inside playground by publishing /gazebo/set_model_state topic
        2. Set a goal point inside playground for red ball
        Returns: 
        init_position: array([x, y]) 
        goal_position: array([x, y])      
        """
        rospy.logdebug("Start initializing robot...")
        # reset the robot velocity to 0:
        self._cmd_vel_pub.publish(Twist())

        #self._reset = True
        # reset simulation to orignal:
        if(self._reset): 
            self._reset = False
            '''
            self.navigation_launcher.restart()
            self.cnn_data_launcher.restart()
            time.sleep(6)
            '''
            self._check_all_systems_ready()
            
            # self.gazebo.resetSim()
            # test whether robot pose is in the map free space:
            self.pos_valid_flag = False
            map = self.map
            while not self.pos_valid_flag:
                # initialize the robot pose:
                seed_initial_pose = random.randint(0, 18)
                print("random",seed_initial_pose)
                self._set_initial_pose(seed_initial_pose)
                # wait for the initial process is ok:
                time.sleep(4)
                # get the current position:
                x = self.curr_pose.position.x
                y = self.curr_pose.position.y
                radius = self.ROBOT_RADIUS
                self.pos_valid_flag = self._is_pos_valid(x, y, radius, map)
                
        # publish inital goal:
        [goal_x, goal_y, goal_yaw] = self._publish_random_goal()

        #[goal_x, goal_y, goal_yaw] = [3, 3, 0]
        #self._publish_goal(3, 3, 0)

        time.sleep(1)    
        self._check_all_systems_ready()

        # initalize info:
        self.init_pose = self.curr_pose # inital_pose.pose.pose
        self.curr_pose = self.curr_pose # inital_pose.pose.pose
        self.robot.set(self.init_pose.position.x, self.init_pose.position.y, goal_x, goal_y, 0, 0, 2 * math.atan2(self.curr_pose.orientation.z, self.curr_pose.orientation.w))
        self.goal_position.x = goal_x
        self.goal_position.y = goal_y
        rospy.logwarn("Robot was initiated as {}".format(self.init_pose))

        # print("init_robot:", self.robot.px, self.robot.py)
        # print("init_real:", self.curr_pose.position.x, self.curr_pose.position.y)

        # reset pose valid flag:
        self.pos_valid_flag = True
        # reset bumper:
        self.bump_flag = False
        # self.bump_num = 0
        # reset the number of iterations:
        self.num_iterations = 0
        # reset distance to goal register:
        self.dist_to_goal_reg = np.zeros(self.DIST_NUM)
        # reset episode done flag:
        self._episode_done = False

        # Give the system a little time to finish initialization
        rospy.logdebug("Finish initialize robot.")

        # start the timer if this is the first path received
        #if self.check_timer is None:
        #    self.check_start()
        
        return self.init_pose, self.goal_position

    def _set_initial_pose(self, seed_initial_pose):
        # seed_initial_pose = 18
        if self.task_num > 1:
            t = self.task_num
            self.task_num = 2
        # seed_initial_pose = 18
        self._pub_initial_model_state(initial_pos_a[self.task_num, seed_initial_pose, 0], initial_pos_a[self.task_num, seed_initial_pose, 1], initial_pos_a[self.task_num, seed_initial_pose, 2])
        time.sleep(1)
        self._pub_initial_position(initial_pos_a[self.task_num, seed_initial_pose, 0], initial_pos_a[self.task_num, seed_initial_pose, 1], initial_pos_a[self.task_num, seed_initial_pose, 2])
        if self.task_num > 1:
            self.task_num = t
    def _set_initial_pose_origin(self, seed_initial_pose):
        if(seed_initial_pose == 0):
            # set turtlebot initial pose in gazebo:
            self._pub_initial_model_state(1, 1, 0)
            time.sleep(1)
            '''
            # reset robot odometry:
            timer = time.time()
            while time.time() - timer < 0.5:
                self._reset_odom_pub.publish(Empty())
            '''
            # reset robot inital pose in rviz:
            # self._pub_initial_position(1, 0, 0.1377)
            self._pub_initial_position(1, 1, 0)
        elif(seed_initial_pose == 1):
            # set turtlebot initial pose in gazebo:
            self._pub_initial_model_state(14, 7, 1.5705)
            time.sleep(1)
            '''
            # reset robot odometry:
            timer = time.time()
            while time.time() - timer < 0.5:
                self._reset_odom_pub.publish(Empty())     
            ''' 
            # reset robot inital pose in rviz:
            # self._pub_initial_position(12.61, 7.5, 1.70)
            self._pub_initial_position(14, 7, 1.5705)
        elif(seed_initial_pose == 2):
            # set turtlebot initial pose in gazebo:
            self._pub_initial_model_state(1, 16, 0)
            time.sleep(1)
            '''
            # reset robot odometry:
            timer = time.time()
            while time.time() - timer < 0.5:
                self._reset_odom_pub.publish(Empty())   
            '''     
            # reset robot inital pose in rviz:
            # self._pub_initial_position(-1, 14.5, 0.13)
            self._pub_initial_position(1, 16, 0)
        elif(seed_initial_pose == 3):
            # set turtlebot initial pose in gazebo:
            self._pub_initial_model_state(14, 22.5, -1.3113)
            time.sleep(1)
            '''
            # reset robot odometry:
            timer = time.time()
            while time.time() - timer < 0.5:
              self._reset_odom_pub.publish(Empty())
            '''
            # reset robot inital pose in rviz:
            # self._pub_initial_position(10.7, 23, -1.16)
            self._pub_initial_position(14, 22.5, -1.3113)
        elif(seed_initial_pose == 4):
            # set turtlebot initial pose in gazebo:
            self._pub_initial_model_state(4, 4, 1.5705)
            time.sleep(1)
            '''
            # reset robot odometry:
            timer = time.time()
            while time.time() - timer < 0.5:
                self._reset_odom_pub.publish(Empty())
            '''
            # reset robot inital pose in rviz:
            # self._pub_initial_position(3.6, 3.1, 1.70)
            self._pub_initial_position(4, 4, 1.5705)
        elif(seed_initial_pose == 5):
            # set turtlebot initial pose in gazebo:
            self._pub_initial_model_state(2, 9, 0)
            time.sleep(1)
            '''
            # reset robot odometry:
            timer = time.time()
            while time.time() - timer < 0.5:
                self._reset_odom_pub.publish(Empty())
            '''
            # reset robot inital pose in rviz:
            # self._pub_initial_position(1, 8, 0.13)
            self._pub_initial_position(2, 9, 0)
        elif(seed_initial_pose == 6):
            # set turtlebot initial pose in gazebo:
            self._pub_initial_model_state(30, 9, 3.14)
            time.sleep(1)
            '''
            # reset robot odometry:
            timer = time.time()
            while time.time() - timer < 0.5:
                self._reset_odom_pub.publish(Empty())
            '''
            # reset robot inital pose in rviz:
            # self._pub_initial_position(28, 11.4, 3.25)
            self._pub_initial_position(30, 9, 3.14)
        elif(seed_initial_pose == 7):
            # set turtlebot initial pose in gazebo:
            self._pub_initial_model_state(25, 17, 3.14)
            time.sleep(1)
            '''
            # reset robot odometry:
            timer = time.time()
            while time.time() - timer < 0.5:
                self._reset_odom_pub.publish(Empty())
            '''
            # reset robot inital pose in rviz:
            # self._pub_initial_position(22.5, 18.8, 3.25)
            self._pub_initial_position(25, 17, 3.14)
        elif(seed_initial_pose == 8):
            # set turtlebot initial pose in gazebo:
            self._pub_initial_model_state(5, 8, 0)
            time.sleep(1)
            '''
            # reset robot odometry:
            timer = time.time()
            while time.time() - timer < 0.5:
                self._reset_odom_pub.publish(Empty())
            '''
            # reset robot inital pose in rviz:
            # self._pub_initial_position(3.96, 7.47, 0.137)
            self._pub_initial_position(5, 8, 0)
        elif(seed_initial_pose == 9):
            # set turtlebot initial pose in gazebo:
            self._pub_initial_model_state(10, 12, 0)
            time.sleep(1)
            '''
            # reset robot odometry:
            timer = time.time()
            while time.time() - timer < 0.5:
                self._reset_odom_pub.publish(Empty())
            '''
            # reset robot inital pose in rviz:
            # self._pub_initial_position(8.29, 12.07, 0.116)
            self._pub_initial_position(10, 12, 0)
        elif(seed_initial_pose == 10):
            # set turtlebot initial pose in gazebo:
            self._pub_initial_model_state(14, 15, 1.576)
            time.sleep(1)
            '''
            # reset robot odometry:
            timer = time.time()
            while time.time() - timer < 0.5:
                self._reset_odom_pub.publish(Empty())
            '''
            # reset robot inital pose in rviz:
            # self._pub_initial_position(11.75, 15.14, 1.729)
            self._pub_initial_position(14, 15, 1.576)
        elif(seed_initial_pose == 11):
            # set turtlebot initial pose in gazebo:
            self._pub_initial_model_state(18.5, 15.7, 3.14)
            time.sleep(1)
            '''
            # reset robot odometry:
            timer = time.time()
            while time.time() - timer < 0.5:
                self._reset_odom_pub.publish(Empty())
            '''
            # reset robot inital pose in rviz:
            # self._pub_initial_position(16.06, 16.7, -2.983)
            self._pub_initial_position(18.5, 15.7, 3.14)
        elif(seed_initial_pose == 12):
            # set turtlebot initial pose in gazebo:
            self._pub_initial_model_state(18.5, 11.3, 3.14)
            time.sleep(1)
            '''
            # reset robot odometry:
            timer = time.time()
            while time.time() - timer < 0.5:
                self._reset_odom_pub.publish(Empty())
            '''
            # reset robot inital pose in rviz:
            # self._pub_initial_position(16.686, 12.434, -2.983)
            self._pub_initial_position(18.5, 11.3, 3.14)
        elif(seed_initial_pose == 13):
            # set turtlebot initial pose in gazebo:
            self._pub_initial_model_state(14, 11.3, 3.14)
            time.sleep(1)
            '''
            # reset robot odometry:
            timer = time.time()
            while time.time() - timer < 0.5:
                self._reset_odom_pub.publish(Empty())
            '''
            # reset robot inital pose in rviz:
            # self._pub_initial_position(12.142, 12.1, -2.983)
            self._pub_initial_position(14, 11.3, 3.14)
        elif(seed_initial_pose == 14):
            # set turtlebot initial pose in gazebo:
            self._pub_initial_model_state(12.5, 13.2, 0.78)
            time.sleep(1)
            '''
            # reset robot odometry:
            timer = time.time()
            while time.time() - timer < 0.5:
                self._reset_odom_pub.publish(Empty())
            '''
            # reset robot inital pose in rviz:
            # self._pub_initial_position(10.538, 13.418, 0.9285)
            self._pub_initial_position(12.5, 13.2, 0.78)
        elif(seed_initial_pose == 15):
            # set turtlebot initial pose in gazebo:
            self._pub_initial_model_state(12.07, 16.06, 0)
            time.sleep(1)
            '''
            # reset robot odometry:
            timer = time.time()
            while time.time() - timer < 0.5:
                self._reset_odom_pub.publish(Empty())
            '''
            # reset robot inital pose in rviz:
            # self._pub_initial_position(9.554, 16.216, 0.143)
            self._pub_initial_position(12.07, 16.06, 0)
        elif(seed_initial_pose == 16):
            # set turtlebot initial pose in gazebo:
            self._pub_initial_model_state(21, 14, -1.576)
            time.sleep(1)
            '''
            # reset robot odometry:
            timer = time.time()
            while time.time() - timer < 0.5:
                self._reset_odom_pub.publish(Empty())
            '''
            # reset robot inital pose in rviz:
            # self._pub_initial_position(18.462, 15.352, -1.4276)
            self._pub_initial_position(21, 14, -1.576)
        elif(seed_initial_pose == 17):
            # set turtlebot initial pose in gazebo:
            self._pub_initial_model_state(14, 22.5, 1.576)
            time.sleep(1)
            '''
            # reset robot odometry:
            timer = time.time()
            while time.time() - timer < 0.5:
                self._reset_odom_pub.publish(Empty())
            '''
            # reset robot inital pose in rviz:
            # self._pub_initial_position(10.586, 22.836, 1.7089)
            self._pub_initial_position(14, 22.5, 1.576)
        elif(seed_initial_pose == 18):
            # set turtlebot initial pose in gazebo:
            self._pub_initial_model_state(18, 8.5, -1.576)
            time.sleep(1)
            '''
            # reset robot odometry:
            timer = time.time()
            while time.time() - timer < 0.5:
                self._reset_odom_pub.publish(Empty())
            '''
            # reset robot inital pose in rviz:
            # self._pub_initial_position(16.551, 9.630, -1.4326)
            self._pub_initial_position(18, 8.5, -1.576)

    def _pub_initial_model_state(self, x, y, theta):
        """
        Publishing new initial position (x, y, theta) 
        :param x x-position of the robot
        :param y y-position of the robot
        :param theta theta-position of the robot
        """
        robot_state = ModelState()
        robot_state.model_name = "mobile_base"
        robot_state.pose.position.x = x
        robot_state.pose.position.y = y
        robot_state.pose.position.z = 0
        robot_state.pose.orientation.x = 0
        robot_state.pose.orientation.y = 0
        robot_state.pose.orientation.z = np.sin(theta/2)
        robot_state.pose.orientation.w = np.cos(theta/2)
        robot_state.reference_frame = "world"
        # publish model_state to set robot
        # self._set_robot_state_publisher.publish(robot_state)
        rospy.wait_for_service('/gazebo/set_model_state')
        try:
            result = self._set_robot_state_service(robot_state)
            rospy.logwarn("set the model state successfully")
        except rospy.ServiceException:
            rospy.logwarn("/gazebo/set_model_state service call failed")   

    def _pub_initial_position(self, x, y, theta):
        """
        Publishing new initial position (x, y, theta) --> for localization
        :param x x-position of the robot
        :param y y-position of the robot
        :param theta theta-position of the robot
        """
        if self.lsy_code:
            x = x-1
            y = y-1
        inital_pose = PoseWithCovarianceStamped()
        inital_pose.header.frame_id = "map"
        inital_pose.header.stamp = rospy.Time.now()
        inital_pose.pose.pose.position.x= x
        inital_pose.pose.pose.position.y= y
        inital_pose.pose.pose.position.z= 0
        inital_pose.pose.pose.orientation.x = 0
        inital_pose.pose.pose.orientation.y = 0
        inital_pose.pose.pose.orientation.z = np.sin(theta/2)
        inital_pose.pose.pose.orientation.w = np.cos(theta/2)
        self._initial_pose_pub.publish(inital_pose)

    def _publish_random_goal(self):
        """
        Publishing new random goal [x, y, theta] for global planner
        :return: goal position [x, y, theta]
        """
        dis_diff = 21
        # only select the random goal in range of 20 m:
        while(dis_diff >= 7 or dis_diff < 4.2): # or dis_diff < 1):
            x, y, theta = self._get_random_pos_on_map(self.map)
            # distance difference：
            dis_diff = np.linalg.norm(
                np.array([
                self.curr_pose.position.x - x,
                self.curr_pose.position.y - y,
                ])
                )
        print(x, y, theta)
        self._publish_goal(x, y, theta)
        return x, y, theta

    def _publish_goal(self, x, y, theta):
        """
        Publishing goal (x, y, theta)
        :param x x-position of the goal
        :param y y-position of the goal
        :param theta theta-position of the goal
        """
        # publish inital goal:
        goal_yaw = theta
        # create message:
        goal = PoseStamped()
        # initialize header:
        goal.header.stamp = rospy.Time.now()
        goal.header.frame_id = "map"
        # initialize pose:
        # position:
        goal.pose.position.x = x - 1
        goal.pose.position.y = y - 1
        goal.pose.position.z = 0
        # orientation:
        goal.pose.orientation.x = 0
        goal.pose.orientation.y = 0
        goal.pose.orientation.z = np.sin(goal_yaw/2)
        goal.pose.orientation.w = np.cos(goal_yaw/2)
        self._initial_goal_pub.publish(goal)

    def _get_random_pos_on_map(self, map):
        """
        Find a valid (free) random poobstaclesition (x, y, theta) on the map
        :param map
        :return: x, y, theta
        """
        map_width = map.info.width * map.info.resolution + map.info.origin.position.x
        map_height = map.info.height * map.info.resolution + map.info.origin.position.y
        x = random.uniform(0.0 , map_width)
        y = random.uniform(0.0, map_height)
        radius = self.ROBOT_RADIUS + 0.5  # safe radius
        while not self._is_pos_valid(x, y, radius, map):
            x = random.uniform(0.0, map_width)
            y = random.uniform(0.0, map_height)

        theta = random.uniform(-math.pi, math.pi)
        return x, y, theta

    def _is_pos_valid(self, x, y, radius, map):
        """
        Checks if position (x,y) is a valid position on the map.
        :param  x: x-position
        :param  y: y-position
        :param  radius: the safe radius of the robot 
        :param  map
        :return: True if position is valid
        """
        cell_radius = int(radius/map.info.resolution)
        y_index =  int((y-map.info.origin.position.y)/map.info.resolution)
        x_index =  int((x-map.info.origin.position.x)/map.info.resolution)

        for i in range(x_index-cell_radius, x_index+cell_radius, 1):
            for j in range(y_index-cell_radius, y_index+cell_radius, 1):
                index = j * map.info.width + i
                if index >= len(map.data):
                    return False
                try:
                    val = map.data[index]
                except IndexError:
                    print("IndexError: index: %d, map_length: %d"%(index, len(map.data)))
                    return False
                if val != 0:
                    return False
        return True
    # ----------------------------

    def reset(self, phase='test', test_case=None):
        # rospy.loginfo("reset")
        """
        Set px, py, gx, gy, vx, vy, theta for robot and humans
        :return:
        """
        assert phase in ['train', 'val', 'test']
        self.phase = phase
        if self.robot is None:
            raise AttributeError('Robot has to be set!')

        if test_case is not None:
            self.case_counter[phase] = test_case
        self.global_time = 0

        base_seed = {'train': self.case_capacity['val'] + self.case_capacity['test'],
                     'val': 0, 'test': self.case_capacity['val']}

        # rospy.logdebug("Reseting RobotGazeboEnvironment")
        self._reset_sim()
        # point = Point()
        # point.x = 5
        # point.y = 5
        # point.z = 0.7 #theta
        # self.robot.set(point.x, point.y,15,15,0,0,point.z)
        # self.reset_pub.publish(point)
        # self._cmd_vel_pub.publish(Twist())
        if self.case_counter[phase] >= 0:
            np.random.seed(base_seed[phase] + self.case_counter[phase])
            random.seed(base_seed[phase] + self.case_counter[phase])
            if phase == 'test':
                logging.debug('current test seed is:{}'.format(base_seed[phase] + self.case_counter[phase]))
            if not self.robot.policy.multiagent_training and phase in ['train', 'val']:
                # only CADRL trains in circle crossing simulation
                human_num = 1
                self.current_scenario = 'circle_crossing'
            else:
                self.current_scenario = self.test_scenario
                human_num = self.human_num
            self.generate_all_humans()
            self.humans = self.all_humans
            # for human in self.all_humans:
            #     print("[",human.px, human.py, human.vx,human.vy,"]")
            # print("next")
            # for human in self.humans:
            #     print("[",human.px, human.py, human.vx,human.vy,"]")
            self.human_num = len(self.all_humans)
            
            # case_counter is always between 0 and case_size[phase]
            self.case_counter[phase] = (self.case_counter[phase] + 1) % self.case_size[phase]
        else:
            assert phase == 'test'
            if self.case_counter[phase] == -1:
                # for debugging purposes
                self.human_num = 3
                self.humans = [Human(self.config, 'humans') for _ in range(self.human_num)]
                self.humans[0].set(0, -6, 0, 5, 0, 0, np.pi / 2)
                self.humans[1].set(-5, -5, -5, 5, 0, 0, np.pi / 2)
                self.humans[2].set(5, -5, 5, 5, 0, 0, np.pi / 2)
            else:
                raise NotImplementedError
        for agent in [self.robot] + self.humans:
            agent.time_step = self.time_step
            agent.policy.time_step = self.time_step

        if self.centralized_planning:
            self.centralized_planner.time_step = self.time_step

        self.states = list()
        self.robot_actions = list()
        self.rewards = list()
        if hasattr(self.robot.policy, 'action_values'):
            self.action_values = list()
        if hasattr(self.robot.policy, 'get_attention_weights'):
            self.attention_weights = list()
        if hasattr(self.robot.policy, 'get_matrix_A'):
            self.As = list()
        if hasattr(self.robot.policy, 'get_feat'):
            self.feats = list()
        if hasattr(self.robot.policy, 'get_X'):
            self.Xs = list()
        if hasattr(self.robot.policy, 'trajs'):
            self.trajs = list()

        # get current observation
        if self.robot.sensor == 'coordinates':
            ob = self.compute_observation_for(self.robot)
        elif self.robot.sensor == 'RGB':
            raise NotImplementedError
        # for o in ob:
        #     print(o.to_tuple())

        return ob, self.obstacles

    def onestep_lookahead(self, action):
        return self.step(action, update=False)

    def step(self, action, update=True):
        """
        Compute actions for all agents, detect collision, update environment and return (ob, reward, done, info)
        """

        # self.env_match = reward_v
        vx_min = 0
        vx_max = 0.5
        vz_min = -2 #-3
        vz_max = 2 #3
        # print("pre:", self.robot.theta)

        self.gazebo.unpauseSim()
        self._take_action(action)
        self.gazebo.pauseSim()
        self.robot.set_position([self.curr_pose.position.x, self.curr_pose.position.y])
        # print("next:", self.curr_pose.position.x)
        #这里真实执行的action和要求执行的action不相同，所以训练数据集里面应该保存真实实行的action
        v = (action[0] + 1) * (vx_max - vx_min) / 2 + vx_min
        w = (action[1] + 1) * (vz_max - vz_min) / 2 + vz_min
        # print("add:",v / w * (np.sin(self.robot.theta) - np.sin(self.robot.theta + w * 0.05)) )
        #这个角度计算方法是对的，可以验证https://quaternions.online/
        theta = 2 * math.atan2(self.curr_pose.orientation.z, self.curr_pose.orientation.w)
        #把theta区间控制在-pi和pi之间
        if theta >= 0:
            if (theta//math.pi) % 2 == 0:
                theta = theta % math.pi
            else:
                theta = theta % math.pi - math.pi
        else:
            if (theta//(-math.pi)) % 2 == 0:
                theta = theta % (-math.pi)
            else:
                theta = theta % (-math.pi) + math.pi
        #角度差计算方式
        # if w>=0:
        #     print("bz:", (theta-self.robot.theta)%(2*math.pi)*20, theta, self.robot.theta)
        # else:
        #     print("bz:", (theta-self.robot.theta)%(-2*math.pi)*20)
        # print("w", w,"ac:", action[1])
        self.robot.set_theta(theta)
        vx = v * np.cos(self.robot.theta)
        vy = v * np.sin(self.robot.theta)
        self.robot.set_velocity([vx, vy])
        self.generate_all_humans()
        for new_human in self.all_humans:
            flag = False
            for i, old_human in enumerate(self.humans):
                if old_human.id == new_human.id:
                    #检查速度是不是正确的
                    # print("cal:", (new_human.px - self.humans[i].px)/0.05, "pre:", self.humans[i].vx, "nex:", new_human.vx)
                    self.humans[i] = new_human
                    flag = True
                    break
            if flag == False:
                self.humans.append(new_human)

        # collsion detection between obstacle and robot
        collision = False
        dmin_p = float('inf')
        for human in self.humans:
            px = human.px - self.curr_pose.position.x
            py = human.py - self.curr_pose.position.y
            closest_dist = np.linalg.norm((px, py)) - 2*self.ROBOT_RADIUS

            if closest_dist < 0:
                collision = True
                break
            elif closest_dist < dmin_p:
                dmin_p = closest_dist
        # print(self.curr_pose.position.x, self.curr_pose.position.y)
        if collision == False:
            dmin_o = float('inf')
            #障碍物是线段不是直线，这里需要根据线段画一个包围盒子
            for obstacle in self.obstacles:
                _, _, closest_dist = point_to_segment_dist(obstacle[0], obstacle[1], obstacle[2], obstacle[3], self.curr_pose.position.x, self.curr_pose.position.y)
                closest_dist = closest_dist - self.ROBOT_RADIUS
                if closest_dist < 0:
                    # print(obstacle[0], obstacle[1], obstacle[2], obstacle[3])
                    # print(self.curr_pose.position.x, self.curr_pose.position.y)
                    # print(closest_dist)
                    collision = True
                    break
                elif closest_dist < dmin_o:
                    dmin_o = closest_dist

        # collision detection between humans
        # human_num = len(self.mht_peds.tracks)
        # for i in range(human_num):
        #     for j in range(i + 1, human_num):
        #         dx = self.mht_peds.tracks[i].pose.pose.position.x - self.mht_peds.tracks[j].pose.pose.position.x
        #         dy = self.mht_peds.tracks[i].pose.pose.position.y - self.mht_peds.tracks[j].pose.pose.position.y
        #         dist = (dx ** 2 + dy ** 2) ** (1 / 2) - self.ROBOT_RADIUS - self.ROBOT_RADIUS
        #         if dist < 0:
        #             # detect collision but don't take humans' collision into account
        #             logging.debug('Collision happens between humans in step()')

        # store state, action value and attention weights
        if hasattr(self.robot.policy, 'action_values'):
            self.action_values.append(self.robot.policy.action_values)
        if hasattr(self.robot.policy, 'get_attention_weights'):
            self.attention_weights.append(self.robot.policy.get_attention_weights())
        if hasattr(self.robot.policy, 'get_matrix_A'):
            self.As.append(self.robot.policy.get_matrix_A())
        if hasattr(self.robot.policy, 'get_feat'):
            self.feats.append(self.robot.policy.get_feat())
        if hasattr(self.robot.policy, 'get_X'):
            self.Xs.append(self.robot.policy.get_X())
        if hasattr(self.robot.policy, 'traj'):
            self.trajs.append(self.robot.policy.get_traj())

        self.global_time += self.time_step
        self.states.append([self.robot.get_full_state(), [human.get_full_state() for human in self.humans],
                            [human.id for human in self.humans]])
        self.robot_actions.append(action)
        

        # compute the observation
        if self.robot.sensor == 'coordinates':
            ob = self.compute_observation_for(self.robot)
        elif self.robot.sensor == 'RGB':
            raise NotImplementedError

        dist_to_goal = np.linalg.norm(
            np.array([
            self.curr_pose.position.x - self.goal_position.x,
            self.curr_pose.position.y - self.goal_position.y,
            ])
        )

        #print("dist_to_goal", dist_to_goal)

        reaching_goal = dist_to_goal <= self.GOAL_RADIUS

        # reward设计
        if self.global_time >= self.time_limit - 1:
            #reward = 0
            done = True
            info = Timeout()
            self._reset = True

        elif collision:
            #reward = self.collision_penalty
            done = True
            info = Collision()
            self._reset = True
        elif reaching_goal:
            #reward = self.success_reward
            done = True
            info = ReachGoal()
            self._reset = True
        elif dmin_p < 2*self.ROBOT_RADIUS or dmin_o < 2*self.ROBOT_RADIUS:#这里dmin没有了
            # adjust the reward based on FPS
            #reward = reward_c + (dmin - self.discomfort_dist) * self.discomfort_penalty_factor * self.time_step + env_match * 0.1 * self.discomfort_penalty_factor * self.time_step + 1 / dist_to_goal * self.discomfort_penalty_factor * self.time_step
            done = False
            if dmin_p >= 2*self.ROBOT_RADIUS:
                info = Discomfort(dmin_o)
            elif dmin_o >= 2*self.ROBOT_RADIUS:
                info = Discomfort(dmin_p)
            else:
                if dmin_p < dmin_o:
                    info = Discomfort(dmin_p)
                else:
                    info = Discomfort(dmin_o)
            # self._reset = True
            #print((dmin - self.discomfort_dist) * self.discomfort_penalty_factor * self.time_step, env_match * 0.1 * self.discomfort_penalty_factor * self.time_step, dist_to_goal * 0.1 * self.discomfort_penalty_factor * self.time_step)
        else:
            #reward = reward_c + env_match * 0.1 * self.discomfort_penalty_factor * self.time_step + 1 / dist_to_goal * 0.1 * self.discomfort_penalty_factor * self.time_step
            done = False
            info = Nothing()
            # self._reset = True

            #print(env_match * 0.1 * self.discomfort_penalty_factor * self.time_step, dist_to_goal * 0.1 * self.discomfort_penalty_factor * self.time_step)

        reward = self._compute_reward()

        self.rewards.append(reward)

        self.num_iterations += 1
            

        return ob, reward, done, info, self.obstacles

    def _compute_reward(self):
        """Calculates the reward to give based on the observations given.
        """
        # reward parameters:
        r_arrival = 20 #20 #15
        r_waypoint = 3.2 #3.2 #2.5 #1.6 #2 #3 #1.6 #6 #2.5 #2.5
        r_collision = -20 #-20 #-15
        r_collision_o = -20 #-19.99
        r_scan = -0.6 #-0.2 #-0.15 #-0.3
        r_angle = 0.6 #0.5 #1 #0.8 #1 #0.5
        r_rotation = -0.1 #-0.1 #-0.15 #-0.4 #-0.5 #-0.2 # 0.1
        r_vector = 0.1

        angle_thresh = np.pi/6
        w_thresh = 1 # 0.7

        # reward parts:
        r_g = self._goal_reached_reward(r_arrival, r_waypoint)
        # r_c = self._obstacle_collision_punish_old(self.cnn_data.scan[-720:], r_scan, r_collision)
        r_c = self._obstacle_collision_punish(r_scan, r_collision, r_collision_o)
        r_w = self._angular_velocity_punish(self.curr_vel.angular.z,  r_rotation, w_thresh)
        # r_t = self._theta_reward(self.goal, self.mht_peds, self.curr_vel.linear.x, r_angle, angle_thresh)
        # r_m = self._env_match_reward(r_vector)
        reward = r_g + r_c + r_w #+ r_m #+ r_t #+ r_v # + r_p
        #rospy.logwarn("Current Velocity: \ncurr_vel = {}".format(self.curr_vel.linear.x))
        # rospy.logwarn("Compute reward done. \nreward = {}".format(reward))
        return reward

    def _env_match_reward(self, r_vector):
        na_human = []
        rg = 3
        for human in self.humans:
            # if agent_state.type == 2:
            #     continue
            px = human.px - self.curr_pose.position.x
            py = human.py - self.curr_pose.position.y
            closest_dist = np.linalg.norm((px, py))
            if closest_dist < rg:
                na_human.append([px, py, human.vx, human.vy])
        na_human = np.array(na_human, np.float32)
        na_human = torch.from_numpy(na_human).to("cuda:0")
        if len(na_human) <= 1:
            reward = 0
            # print(len(na_human))
        else:
            # print(len(na_human))
            # a = time.time()
            # print(len(na_human))
            # print(na_human.expand([1,-1,-1]))
            hu_vector = self.human_vector_predictor(na_human.expand([1,-1,-1]))
            # b = time.time()
            #计算当前动作符合运动场的程度,向量相似度+场景密度
            # vx = real_action.v * np.cos(real_action.r + robot_state.theta)
            # vy = real_action.v * np.sin(real_action.r + robot_state.theta)
            if (hu_vector[0][0] ** 2 + hu_vector[0][1] ** 2) == 0 or (self.robot.vx ** 2 + self.robot.vy ** 2) == 0:
                reward = 0
            else:
                #这里用余弦相似度计算方向相似性，不考虑大小，大小实际上在碰撞的奖励函数里面就实现了
                reward = (hu_vector[0][0] * self.robot.vx + hu_vector[0][1] * self.robot.vy) / math.sqrt(hu_vector[0][0] ** 2 + hu_vector[0][1] ** 2) / math.sqrt(self.robot.vx ** 2 + self.robot.vy ** 2)
                reward = reward - 1
                #奖励加上权重引子和环境稀疏程度因子
                # print("match:", reward_v)
                reward = reward * r_vector * len(na_human) / 30
        # rospy.logwarn("env match reward: {}".format(reward))
        return reward

    def _goal_reached_reward(self, r_arrival, r_waypoint):
        """
        Returns positive reward if the robot reaches the goal.
        :param transformed_goal goal position in robot frame
        :param k reward constant
        :return: returns reward colliding with obstacles
        """
        # distance to goal:
        dist_to_goal = np.linalg.norm(
            np.array([
            self.curr_pose.position.x - self.goal_position.x,
            self.curr_pose.position.y - self.goal_position.y,
            ])
        )
        # t-1 id:
        t_1 = self.num_iterations % self.DIST_NUM
        # initialize the dist_to_goal_reg:
        if(self.num_iterations == 0):
            #self.dist_to_goal_reg = np.ones(self.DIST_NUM)*dist_to_goal
            self.dist_to_goal_reg = dist_to_goal

        # rospy.logwarn("distance_to_goal_reg = {}".format(self.dist_to_goal_reg))
        # rospy.logwarn("distance_to_goal = {}".format(dist_to_goal))
        max_iteration = 300 #800 
        # reward calculation:
        if(dist_to_goal <= self.GOAL_RADIUS):  # goal reached: t = T
            reward = r_arrival
        # elif(self.num_iterations >= max_iteration):  # failed to the goal
        elif(self.global_time >= self.time_limit):
            reward = -r_arrival
        else:   # on the way
            reward = r_waypoint*(self.dist_to_goal_reg - dist_to_goal)
            # reward = 0

        # storage the robot pose at t-1:
        #if(self.num_iterations % 40 == 0):
        self.dist_to_goal_reg = dist_to_goal #self.curr_pose
    
        rospy.logwarn("Goal reached reward: {}".format(reward))
        return reward

    def _obstacle_collision_punish_old(self, scan, r_scan, r_collision):
        """
        Returns negative reward if the robot collides with obstacles.
        :param scan containing obstacles that should be considered
        :param k reward constant
        :return: returns reward colliding with obstacles
        """
        min_scan_dist = np.amin(scan[scan!=0])
        #if(self.bump_flag == True): #or self.pos_valid_flag == False):
        if(min_scan_dist <= self.ROBOT_RADIUS and min_scan_dist >= 0.02):
            reward = r_collision
        elif(min_scan_dist < 3*self.ROBOT_RADIUS):
            reward = r_scan * (3*self.ROBOT_RADIUS - min_scan_dist)
        else:
            reward = 0.0

        # rospy.logwarn("Obstacle collision reward: {}".format(reward))
        return reward

    def _obstacle_collision_punish(self, r_scan, r_collision, r_collision_o):
        #判断是否发生碰撞
        dmin = float('inf')
        collision = False
        for human in self.humans:
            # if agent_state.type == 2:
            #     continue
            px = human.px - self.curr_pose.position.x
            py = human.py - self.curr_pose.position.y
            closest_dist = np.linalg.norm((px, py)) - 2*self.ROBOT_RADIUS

            if closest_dist < 0:
                collision = True
                reward = r_collision
                break
            elif closest_dist < dmin:
                dmin = closest_dist
                # print(dmin)

        if collision:
            # reward = r_collision
            rospy.logwarn("Obstacle collision reward: {}".format(reward))
            return reward
        elif dmin < 2 * self.ROBOT_RADIUS:
            reward = r_scan * (2 * self.ROBOT_RADIUS - dmin)
        else: 
            reward = 0
        
        dmin = float('inf')
        # collision = False
        #判断是否会和静态障碍物发生碰撞
        for obstacle in self.obstacles:
            # if obstacle[2] == obstacle[0]:
            #     if self.curr_pose.position.y > obstacle[1] and self.curr_pose.position.y > obstacle[3] or self.curr_pose.position.y < obstacle[1] and self.curr_pose.position.y < obstacle[3]:
            #         if np.linalg.norm((obstacle[0] - self.curr_pose.position.x, obstacle[1] - self.curr_pose.position.y)) > np.linalg.norm((obstacle[2] - self.curr_pose.position.x, obstacle[3] - self.curr_pose.position.y)):
            #             closest_dist = np.linalg.norm((obstacle[2] - self.curr_pose.position.x, obstacle[3] - self.curr_pose.position.y))
            #         else:
            #             closest_dist = np.linalg.norm((obstacle[0] - self.curr_pose.position.x, obstacle[1] - self.curr_pose.position.y))
            #     else:
            #         closest_dist = abs(self.curr_pose.position.x - obstacle[2])
            # elif obstacle[3] == obstacle[1]:
            #     if self.curr_pose.position.y > obstacle[1] and self.curr_pose.position.y > obstacle[3] or self.curr_pose.position.y < obstacle[1] and self.curr_pose.position.y < obstacle[3]:
            #         if np.linalg.norm((obstacle[0] - self.curr_pose.position.x, obstacle[1] - self.curr_pose.position.y)) > np.linalg.norm((obstacle[2] - self.curr_pose.position.x, obstacle[3] - self.curr_pose.position.y)):
            #             closest_dist = np.linalg.norm((obstacle[2] - self.curr_pose.position.x, obstacle[3] - self.curr_pose.position.y))
            #         else:
            #             closest_dist = np.linalg.norm((obstacle[0] - self.curr_pose.position.x, obstacle[1] - self.curr_pose.position.y))
            #     else:
            #         closest_dist = abs(self.curr_pose.position.y - obstacle[3])
            # else:
            #     # closest_dist = abs(obstacle[1] + (obstacle[3] - obstacle[1]) / (obstacle[2] - obstacle[0]) * (self.curr_pose.position.x - obstacle[0]) - self.curr_pose.position.y)
            #     _, _, closest_dist = point_to_segment_dist(obstacle[0], obstacle[1], obstacle[2], obstacle[3], self.curr_pose.position.x, self.curr_pose.position.y)
            # if closest_dist < 0.3:
            #     collision = True
            #     reward = reward - 15
            #     break
            # elif closest_dist < dmin:
            #     dmin = closest_dist
            _, _, closest_dist = point_to_segment_dist(obstacle[0], obstacle[1], obstacle[2], obstacle[3], self.curr_pose.position.x, self.curr_pose.position.y)
            closest_dist = closest_dist - self.ROBOT_RADIUS
            if closest_dist < 0:
                collision = True
                reward = r_collision_o
                break
            elif closest_dist < dmin:
                dmin = closest_dist
        
        if collision:
            rospy.logwarn("Obstacle collision reward: {}".format(reward))
            return reward
        elif dmin < 2*self.ROBOT_RADIUS:
            reward = reward + r_scan * (2*self.ROBOT_RADIUS - dmin)
        
        rospy.logwarn("Obstacle collision reward: {}".format(reward))
        return reward

    def _angular_velocity_punish(self, w_z,  r_rotation, w_thresh):
        """
        Returns negative reward if the robot turns.
        :param w roatational speed of the robot
        :param fac weight of reward punish for turning
        :param thresh rotational speed > thresh will be punished
        :return: returns reward for turning
        """
        if(abs(w_z) > w_thresh):
            reward = abs(w_z) * r_rotation
        else:
            reward = 0.0

        # rospy.logwarn("Angular velocity punish reward: {}".format(reward))
        return reward

    def compute_observation_for(self, agent):
        if agent == self.robot:
            ob = []
            # ob_d = []
            for human in self.humans:
                # if norm((self.curr_pose.position.x - human.px, self.curr_pose.position.y - human.py)) < 7:
                ob.append(human.get_observable_state())
            # if len(ob) < 20:
            #     idx = np.random.permutation(len(ob))
            #     idx = np.concatenate([np.random.randint(len(ob), size=20-len(ob)), idx])
            #     for i in idx:
            #         ob_d.append(ob[i])
                # ob = ob[idx[:20]]
                # print(human.get_observable_state().to_tuple())
        else:
            ob = [other_human.get_observable_state() for other_human in self.humans if other_human != agent]
            if self.robot.visible:
                ob += [self.robot.get_observable_state()]
        return ob

    def render(self, mode='video', output_file=None):
        from matplotlib import animation
        import matplotlib.pyplot as plt
        # plt.rcParams['animation.ffmpeg_path'] = '/usr/bin/ffmpeg'
        x_offset = 0.2
        y_offset = 0.4
        cmap = plt.cm.get_cmap('hsv', 10)
        robot_color = 'black'
        arrow_style = patches.ArrowStyle("->", head_length=4, head_width=2)
        display_numbers = True

        if mode == 'traj':
            fig, ax = plt.subplots(figsize=(7, 7))
            ax.tick_params(labelsize=16)
            ax.set_xlim(-5, 5)
            ax.set_ylim(-5, 5)
            ax.set_xlabel('x(m)', fontsize=16)
            ax.set_ylabel('y(m)', fontsize=16)

            # add human start positions and goals
            human_colors = [cmap(i) for i in range(len(self.humans))]
            for i in range(len(self.humans)):
                human = self.humans[i]
                human_goal = mlines.Line2D([human.get_goal_position()[0]], [human.get_goal_position()[1]],
                                           color=human_colors[i],
                                           marker='*', linestyle='None', markersize=15)
                ax.add_artist(human_goal)
                human_start = mlines.Line2D([human.get_start_position()[0]], [human.get_start_position()[1]],
                                            color=human_colors[i],
                                            marker='o', linestyle='None', markersize=15)
                ax.add_artist(human_start)

            robot_positions = [self.states[i][0].position for i in range(len(self.states))]
            human_positions = [[self.states[i][1][j].position for j in range(len(self.humans))]
                               for i in range(len(self.states))]

            for k in range(len(self.states)):
                if k % 4 == 0 or k == len(self.states) - 1:
                    robot = plt.Circle(robot_positions[k], self.robot.radius, fill=False, color=robot_color)
                    humans = [plt.Circle(human_positions[k][i], self.humans[i].radius, fill=False, color=cmap(i))
                              for i in range(len(self.humans))]
                    ax.add_artist(robot)
                    for human in humans:
                        ax.add_artist(human)

                # add time annotation
                global_time = k * self.time_step
                if global_time % 4 == 0 or k == len(self.states) - 1:
                    agents = humans + [robot]
                    times = [plt.text(agents[i].center[0] - x_offset, agents[i].center[1] - y_offset,
                                      '{:.1f}'.format(global_time),
                                      color='black', fontsize=14) for i in range(self.human_num + 1)]
                    for time in times:
                       ax.add_artist(time)
                if k != 0:
                    nav_direction = plt.Line2D((self.states[k - 1][0].px, self.states[k][0].px),
                                               (self.states[k - 1][0].py, self.states[k][0].py),
                                               color=robot_color, ls='solid')
                    human_directions = [plt.Line2D((self.states[k - 1][1][i].px, self.states[k][1][i].px),
                                                   (self.states[k - 1][1][i].py, self.states[k][1][i].py),
                                                   color=cmap(i), ls='solid')
                                        for i in range(self.human_num)]
                    ax.add_artist(nav_direction)
                    for human_direction in human_directions:
                        ax.add_artist(human_direction)
            plt.legend([robot], ['Robot'], fontsize=16)
            plt.show()
        elif mode == 'video':
            fig, ax = plt.subplots(figsize=(7, 7))
            ax.tick_params(labelsize=12)
            ax.set_xlim(-11, 11)
            ax.set_ylim(-11, 11)
            ax.set_xlabel('x(m)', fontsize=14)
            ax.set_ylabel('y(m)', fontsize=14)
            show_human_start_goal = False

            # add human start positions and goals
            human_colors = [cmap(i) for i in range(len(self.humans))]
            if show_human_start_goal:
                for i in range(len(self.humans)):
                    human = self.humans[i]
                    human_goal = mlines.Line2D([human.get_goal_position()[0]], [human.get_goal_position()[1]],
                                               color=human_colors[i],
                                               marker='*', linestyle='None', markersize=8)
                    ax.add_artist(human_goal)
                    human_start = mlines.Line2D([human.get_start_position()[0]], [human.get_start_position()[1]],
                                                color=human_colors[i],
                                                marker='o', linestyle='None', markersize=8)
                    ax.add_artist(human_start)
            # add robot start position
            robot_start = mlines.Line2D([self.robot.get_start_position()[0]], [self.robot.get_start_position()[1]],
                                        color=robot_color,
                                        marker='o', linestyle='None', markersize=8)
            robot_start_position = [self.robot.get_start_position()[0], self.robot.get_start_position()[1]]
            ax.add_artist(robot_start)
            # add robot and its goal
            robot_positions = [state[0].position for state in self.states]
            goal = mlines.Line2D([self.robot.get_goal_position()[0]], [self.robot.get_goal_position()[1]],
                                 color=robot_color, marker='*', linestyle='None',
                                 markersize=15, label='Goal')
            robot = plt.Circle(robot_positions[0], self.robot.radius, fill=False, color=robot_color)
            # sensor_range = plt.Circle(robot_positions[0], self.robot_sensor_range, fill=False, ls='dashed')
            ax.add_artist(robot)
            ax.add_artist(goal)
            plt.legend([robot, goal], ['Robot', 'Goal'], fontsize=14)

            # add humans and their numbers
            human_positions = [[state[1][j].position for j in range(len(self.humans))] for state in self.states]
            humans = [plt.Circle(human_positions[0][i], self.humans[i].radius, fill=False, color=cmap(i))
                      for i in range(len(self.humans))]

            # disable showing human numbers
            if display_numbers:
                human_numbers = [plt.text(humans[i].center[0] - x_offset, humans[i].center[1] + y_offset, str(i),
                                          color='black') for i in range(len(self.humans))]

            for i, human in enumerate(humans):
                ax.add_artist(human)
                if display_numbers:
                    ax.add_artist(human_numbers[i])

            # add time annotation
            time = plt.text(0.4, 0.9, 'Time: {}'.format(0), fontsize=16, transform=ax.transAxes)
            ax.add_artist(time)

            # visualize attention scores
            # if hasattr(self.robot.policy, 'get_attention_weights'):
            #     attention_scores = [
            #         plt.text(-5.5, 5 - 0.5 * i, 'Human {}: {:.2f}'.format(i + 1, self.attention_weights[0][i]),
            #                  fontsize=16) for i in range(len(self.humans))]

            # compute orientation in each step and use arrow to show the direction
            radius = self.robot.radius
            orientations = []
            for i in range(self.human_num + 1):
                orientation = []
                for state in self.states:
                    agent_state = state[0] if i == 0 else state[1][i - 1]
                    if self.robot.kinematics == 'unicycle' and i == 0:
                        direction = (
                        (agent_state.px, agent_state.py), (agent_state.px + radius * np.cos(agent_state.theta),
                                                           agent_state.py + radius * np.sin(agent_state.theta)))
                    else:
                        theta = np.arctan2(agent_state.vy, agent_state.vx)
                        direction = ((agent_state.px, agent_state.py), (agent_state.px + radius * np.cos(theta),
                                                                        agent_state.py + radius * np.sin(theta)))
                    orientation.append(direction)
                orientations.append(orientation)
                if i == 0:
                    arrow_color = 'black'
                    arrows = [patches.FancyArrowPatch(*orientation[0], color=arrow_color, arrowstyle=arrow_style)]
                else:
                    arrows.extend(
                        [patches.FancyArrowPatch(*orientation[0], color=human_colors[i - 1], arrowstyle=arrow_style)])

            for arrow in arrows:
                ax.add_artist(arrow)
            global_step = 0

            if len(self.trajs) != 0:
                human_future_positions = []
                human_future_circles = []
                for traj in self.trajs:
                    human_future_position = [[tensor_to_joint_state(traj[step+1][0]).human_states[i].position
                                              for step in range(self.robot.policy.planning_depth)]
                                             for i in range(self.human_num)]
                    human_future_positions.append(human_future_position)

                for i in range(self.human_num):
                    circles = []
                    for j in range(self.robot.policy.planning_depth):
                        circle = plt.Circle(human_future_positions[0][i][j], self.humans[0].radius/(1.7+j), fill=False, color=cmap(i))
                        ax.add_artist(circle)
                        circles.append(circle)
                    human_future_circles.append(circles)

            def update(frame_num):
                nonlocal global_step
                nonlocal arrows
                global_step = frame_num
                robot.center = robot_positions[frame_num]

                for i, human in enumerate(humans):
                    human.center = human_positions[frame_num][i]
                    if display_numbers:
                        human_numbers[i].set_position((human.center[0] - x_offset, human.center[1] + y_offset))
                for arrow in arrows:
                    arrow.remove()

                for i in range(self.human_num + 1):
                    orientation = orientations[i]
                    if i == 0:
                        arrows = [patches.FancyArrowPatch(*orientation[frame_num], color='black',
                                                          arrowstyle=arrow_style)]
                    else:
                        arrows.extend([patches.FancyArrowPatch(*orientation[frame_num], color=cmap(i - 1),
                                                               arrowstyle=arrow_style)])

                for arrow in arrows:
                    ax.add_artist(arrow)
                    # if hasattr(self.robot.policy, 'get_attention_weights'):
                    #     attention_scores[i].set_text('human {}: {:.2f}'.format(i, self.attention_weights[frame_num][i]))

                time.set_text('Time: {:.2f}'.format(frame_num * self.time_step))

                if len(self.trajs) != 0:
                    for i, circles in enumerate(human_future_circles):
                        for j, circle in enumerate(circles):
                            circle.center = human_future_positions[global_step][i][j]

            def plot_value_heatmap():
                if self.robot.kinematics != 'holonomic':
                    print('Kinematics is not holonomic')
                    return
                # for agent in [self.states[global_step][0]] + self.states[global_step][1]:
                #     print(('{:.4f}, ' * 6 + '{:.4f}').format(agent.px, agent.py, agent.gx, agent.gy,
                #                                              agent.vx, agent.vy, agent.theta))

                # when any key is pressed draw the action value plot
                fig, axis = plt.subplots()
                speeds = [0] + self.robot.policy.speeds
                rotations = self.robot.policy.rotations + [np.pi * 2]
                r, th = np.meshgrid(speeds, rotations)
                z = np.array(self.action_values[global_step % len(self.states)][1:])
                z = (z - np.min(z)) / (np.max(z) - np.min(z))
                z = np.reshape(z, (self.robot.policy.rotation_samples, self.robot.policy.speed_samples))
                polar = plt.subplot(projection="polar")
                polar.tick_params(labelsize=16)
                mesh = plt.pcolormesh(th, r, z, vmin=0, vmax=1)
                plt.plot(rotations, r, color='k', ls='none')
                plt.grid()
                cbaxes = fig.add_axes([0.85, 0.1, 0.03, 0.8])
                cbar = plt.colorbar(mesh, cax=cbaxes)
                cbar.ax.tick_params(labelsize=16)
                plt.show()

            def print_matrix_A():
                # with np.printoptions(precision=3, suppress=True):
                #     print(self.As[global_step])
                h, w = self.As[global_step].shape
                print('   ' + ' '.join(['{:>5}'.format(i - 1) for i in range(w)]))
                for i in range(h):
                    print('{:<3}'.format(i-1) + ' '.join(['{:.3f}'.format(self.As[global_step][i][j]) for j in range(w)]))
                # with np.printoptions(precision=3, suppress=True):
                #     print('A is: ')
                #     print(self.As[global_step])

            def print_feat():
                with np.printoptions(precision=3, suppress=True):
                    print('feat is: ')
                    print(self.feats[global_step])

            def print_X():
                with np.printoptions(precision=3, suppress=True):
                    print('X is: ')
                    print(self.Xs[global_step])

            def on_click(event):
                if anim.running:
                    anim.event_source.stop()
                    if event.key == 'a':
                        if hasattr(self.robot.policy, 'get_matrix_A'):
                            print_matrix_A()
                        if hasattr(self.robot.policy, 'get_feat'):
                            print_feat()
                        if hasattr(self.robot.policy, 'get_X'):
                            print_X()
                        # if hasattr(self.robot.policy, 'action_values'):
                        #    plot_value_heatmap()
                else:
                    anim.event_source.start()
                anim.running ^= True

            fig.canvas.mpl_connect('key_press_event', on_click)
            anim = animation.FuncAnimation(fig, update, frames=len(self.states), interval=self.time_step * 500)
            anim.running = True

            if output_file is not None:
                # save as video
                ffmpeg_writer = animation.FFMpegWriter(fps=10, metadata=dict(artist='Me'), bitrate=1800)
                # writer = ffmpeg_writer(fps=10, metadata=dict(artist='Me'), bitrate=1800)
                anim.save(output_file, writer=ffmpeg_writer)

                # save output file as gif if imagemagic is installed
                # anim.save(output_file, writer='imagemagic', fps=12)
            else:
                plt.show()
        else:
            raise NotImplementedError
