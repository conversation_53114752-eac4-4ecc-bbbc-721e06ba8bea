<launch>

<!-- MoveBaseSeq node launch and Points Sequence ROS Parameters Loading -->

    <!--Run move_base_seqs node-->
        <node pkg="drl_vo_nav" type="publish_goal_sequence.py" name="publish_goal" output="screen">
        
        <!-- Load Sequence of Points (Cartesian Coordinates wrt "map" RF, in meters) -->
            <rosparam param="p_seq">[6,0,0, 7,7,0, 10,9.5,0, 17,9.5,0, 24,7.5,0, 
                                     18,6.5,0, 17,1,0, 17,-5,0, 22,-6,0, 27,-4,0, 
                                     27,1.5,0, 23,3.4,0, 18,2.3,0, 19,-3,0, 22,-6.4,0, 
                                     23,2,0, 18,6.7,0, 15.5,10,0, 13,6,0, 11,9.5,0, 
                                     6,6.5,0, 6,0,0, 13,-1,0, 19,-0.5,0, 20.5,6,0]</rosparam>
        <!-- Load Sequence of Desired Yaw Angles (no rotations around x and y axes) in degrees-->
            <rosparam param="yea_seq">[0,0,0,0,0,0,0,0,0,0,
                                       0,0,0,0,0,0,0,0,0,0,
                                       0,0,0,0,0,0,0,0,0,0]</rosparam>

        </node>

</launch>
