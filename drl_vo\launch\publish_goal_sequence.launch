<launch>

<!-- MoveBaseSeq node launch and Points Sequence ROS Parameters Loading -->

    <!--Run move_base_seqs node-->
        <node pkg="drl_vo_nav" type="publish_goal_sequence.py" name="publish_goal" output="screen">
        
        <!-- Load Sequence of Points (Cartesian Coordinates wrt "map" RF, in meters) -->
            <rosparam param="p_seq">[3,4,0, 3,8,0, 5,15,0, 14,18,0, 18,20,0,
                                    19,15,0, 22,11,0, 16,12,0, 8,10,0, 3,9,0,
                                    2,13,0, 5,16,0, 12,18,0, 18,19,0, 14,17,0, 
                                    11,11,0, 17,12,0, 19,11,0, 22,11,0, 19,16,0, 
                                    20,19,0, 12,19,0, 8,16,0, 4,13,0, 3,8,0]</rosparam>
        <!-- Load Sequence of Desired Yaw Angles (no rotations around x and y axes) in degrees-->
            <rosparam param="yea_seq">[0,0,0,0,0,0,0,0,0,0,
                                       0,0,0,0,0,0,0,0,0,0,
                                       0,0,0,0,0,0,0,0,0,0]</rosparam>

        </node>

</launch>
