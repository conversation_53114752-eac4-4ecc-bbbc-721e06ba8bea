<launch>
  <arg name="scene_file" default="$(find pedsim_simulator)/scenarios/mode1.xml"/>
  <arg name="world_name" default="$(find pedsim_gazebo_plugin)/worlds/mode1.world"/>
  <arg name="gui" default="true" doc="Bring up the Gazebo graphical interface"/>
  <arg name="pose_initial_x" default="1.0"/> 
  <arg name="pose_initial_y" default="1.0"/>
  <arg name="map_file" default="$(find robot_gazebo)/maps/mode1/mode1.yaml"/>
  <arg name="initial_pose_x" default="0"/>
  <arg name="initial_pose_y" default="0"/>
  <arg name="initial_pose_a" default="0"/>
  <!-- <arg name="model_file" default="$(find drl_vo_nav)/src/model/drl_pre_train.zip"/> -->
  <arg name="log_dir"   default="$(find drl_vo_nav)/src/runs"/>
  <!-- Output -->
  <arg name="output" default="log"/>
  <arg name="enable_opencv" default="true"/>
  <arg name="enable_console_output" default="true"/>
  <arg name="rviz" default="true"/> 

  <!-- Pedsim Gazebo -->
  <include file="$(find pedsim_simulator)/launch/robot.launch">
    <arg name="scene_file" value="$(arg scene_file)"/>
    <arg name="world_name" value="$(arg world_name)"/>
    <arg name="gui" value="$(arg gui)"/>
    <arg name="pose_initial_x" value="$(arg pose_initial_x)"/> 
    <arg name="pose_initial_y" value="$(arg pose_initial_y)"/>
  </include>

  <!-- AMCL -->
  <include file="$(find robot_gazebo)/launch/amcl_demo_drl.launch">
    <arg name="map_file" value="$(arg map_file)"/>
    <arg name="initial_pose_x" value="$(arg initial_pose_x)"/>
    <arg name="initial_pose_y" value="$(arg initial_pose_y)"/>
    <arg name="initial_pose_a" value="$(arg initial_pose_a)"/>
  </include>

  <!-- CNN DATA -->
  <include file="$(find drl_vo_nav)/launch/nav_cnn_data.launch"/>

  <!-- DRL-VO Control Policy -->
  <include file="$(find drl_vo_nav)/launch/multi_mode_train.launch">
    <arg name="log_dir" value="$(arg log_dir)"/>
  </include>

  <!-- Rviz -->
  <include file="$(find robot_gazebo)/launch/view_navigation.launch" if="$(arg rviz)"/>

</launch>