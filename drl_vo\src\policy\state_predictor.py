import torch
import torch.nn as nn
import numpy as np
from policy.helpers import mlp
import math
from torch.nn import functional as F
import time


class StatePredictor(nn.Module):
    def __init__(self, config, graph_model, time_step):
        """
        This function predicts the next state given the current state as input.
        It uses a graph model to encode the state into a latent space and predict each human's next state.
        """
        super().__init__()
        self.trainable = True
        self.kinematics = config.action_space.kinematics
        self.graph_model = graph_model
        self.human_motion_predictor = mlp(config.gcn.X_dim, config.model_predictive_rl.motion_predictor_dims)
        self.time_step = time_step

    def forward(self, state, action, vars=None, detach=False):
        """ Predict the next state tensor given current state as input.

        :return: tensor of shape (batch_size, # of agents, feature_size)
        """
        assert len(state[0].shape) == 3
        assert len(state[1].shape) == 3

        state_embedding = self.graph_model(state)
        if detach:
            state_embedding = state_embedding.detach()
        if action is None:
            # for training purpose
            next_robot_state = None
        else:
            next_robot_state = self.compute_next_state(state[0], action)
        next_human_states = self.human_motion_predictor(state_embedding)[:, 1:, :]

        next_observation = [next_robot_state, next_human_states]
        return next_observation

    def compute_next_state(self, robot_state, action):
        # currently it can not perform parallel computation
        if robot_state.shape[0] != 1:
            raise NotImplementedError

        # px, py, vx, vy, radius, gx, gy, v_pref, theta
        next_state = robot_state.clone().squeeze()
        if self.kinematics == 'holonomic':
            next_state[0] = next_state[0] + action.vx * self.time_step
            next_state[1] = next_state[1] + action.vy * self.time_step
            next_state[2] = action.vx
            next_state[3] = action.vy
        else:
            next_state[7] = next_state[7] + action.r
            next_state[0] = next_state[0] + math.cos(next_state[7]) * action.v * self.time_step
            next_state[1] = next_state[1] + math.sin(next_state[7]) * action.v * self.time_step
            next_state[2] = math.cos(next_state[7]) * action.v
            next_state[3] = math.sin(next_state[7]) * action.v

        return next_state.unsqueeze(0).unsqueeze(0)

class StatePredictor_maml(nn.Module):
    def __init__(self, config, graph_model, time_step):
        """
        This function predicts the next state given the current state as input.
        It uses a graph model to encode the state into a latent space and predict each human's next state.
        """
        super().__init__()
        self.trainable = True
        self.graph_maml = True
        self.vars = nn.ParameterList()
        self.kinematics = config.action_space.kinematics
        self.graph_model = graph_model
        # self.vars.extend(self.graph_model.parameters())
        self.s_len = 0
        # self.human_motion_predictor = mlp(config.gcn.X_dim, config.model_predictive_rl.motion_predictor_dims)
        self.dims = [config.gcn.X_dim] + config.model_predictive_rl.motion_predictor_dims
        for i in range(len(self.dims) - 1):
            # w = nn.Parameter(torch.randn([self.dims[i+1], self.dims[i]]))
            w = nn.Linear(self.dims[i], self.dims[i+1]).weight
            self.vars.append(w)
            # b = nn.Parameter(torch.randn(self.dims[i+1]))
            b = nn.Linear(self.dims[i], self.dims[i+1]).bias 
            self.vars.append(b)
            self.s_len += 2
        self.vars.extend(self.graph_model.parameters())
        self.time_step = time_step


    def forward(self, state, action, vars=None, detach=False):
        """ Predict the next state tensor given current state as input.

        :return: tensor of shape (batch_size, # of agents, feature_size)
        """
        assert len(state[0].shape) == 3
        assert len(state[1].shape) == 3

        if vars is None:
            vars = self.vars
        # t = time.time()
        if self.graph_maml:
            state_embedding = self.graph_model(state, vars[self.s_len:])
        else:
            state_embedding = self.graph_model(state)
        # d = time.time()
        if detach:
            state_embedding = state_embedding.detach()

        if action is None:
            # for training purpose
            next_robot_state = None
        else:
            next_robot_state = self.compute_next_state(state[0], action)
        # next_human_states = self.human_motion_predictor(state_embedding)[:, 1:, :]
        x = state_embedding
        idx = 0
        
        # print(len(self.dims))
        for i in range(len(self.dims) - 1):
            w, b = vars[idx], vars[idx + 1]
            x = F.linear(x, w, b)
            idx += 2
            if i != len(self.dims) - 2:
                x = F.relu(x)
        next_human_states = x[:, 1:, :]

        next_observation = [next_robot_state, next_human_states]
        
        # print("t", d-t)
        return next_observation

    def compute_next_state(self, robot_state, action):
        # currently it can not perform parallel computation
        if robot_state.shape[0] != 1:
            raise NotImplementedError

        # px, py, vx, vy, radius, gx, gy, v_pref, theta
        next_state = robot_state.clone().squeeze()
        if self.kinematics == 'holonomic':
            next_state[0] = next_state[0] + action.vx * self.time_step
            next_state[1] = next_state[1] + action.vy * self.time_step
            next_state[2] = action.vx
            next_state[3] = action.vy
        else:
            next_state[7] = next_state[7] + action.r
            next_state[0] = next_state[0] + math.cos(next_state[7]) * action.v * self.time_step
            next_state[1] = next_state[1] + math.sin(next_state[7]) * action.v * self.time_step
            next_state[2] = math.cos(next_state[7]) * action.v
            next_state[3] = math.sin(next_state[7]) * action.v

        return next_state.unsqueeze(0).unsqueeze(0)

    def parameters(self):
        """
        override this function since initial parameters will return with a generator.
        :return:
        """
        return self.vars

    def set_parameters(self, vars):
        """
        override this function since initial parameters will return with a generator.
        :return:
        """
        self.vars = vars
        self.graph_model.set_parameters(vars[self.s_len:])


class LinearStatePredictor(object):
    def __init__(self, config, time_step):
        """
        This function predicts the next state given the current state as input.
        It uses a graph model to encode the state into a latent space and predict each human's next state.
        """
        super().__init__()
        self.trainable = False
        self.kinematics = config.action_space.kinematics
        self.time_step = time_step

    def __call__(self, state, action):
        """ Predict the next state tensor given current state as input.

        :return: tensor of shape (batch_size, # of agents, feature_size)
        """
        assert len(state[0].shape) == 3
        assert len(state[1].shape) == 3

        next_robot_state = self.compute_next_state(state[0], action)
        next_human_states = self.linear_motion_approximator(state[1])

        next_observation = [next_robot_state, next_human_states]
        return next_observation

    def compute_next_state(self, robot_state, action):
        # currently it can not perform parallel computation
        if robot_state.shape[0] != 1:
            raise NotImplementedError

        # px, py, vx, vy, radius, gx, gy, v_pref, theta
        next_state = robot_state.clone().squeeze()
        if self.kinematics == 'holonomic':
            next_state[0] = next_state[0] + action.vx * self.time_step
            next_state[1] = next_state[1] + action.vy * self.time_step
            next_state[2] = action.vx
            next_state[3] = action.vy
        else:
            next_state[7] = next_state[7] + action.r
            next_state[0] = next_state[0] + np.cos(next_state[7]) * action.v * self.time_step
            next_state[1] = next_state[1] + np.sin(next_state[7]) * action.v * self.time_step
            next_state[2] = np.cos(next_state[7]) * action.v
            next_state[3] = np.sin(next_state[7]) * action.v

        return next_state.unsqueeze(0).unsqueeze(0)

    @staticmethod
    def linear_motion_approximator(human_states):
        """ approximate human states with linear motion, input shape : (batch_size, human_num, human_state_size)
        """
        # px, py, vx, vy, radius
        next_state = human_states.clone().squeeze()
        next_state[:, 0] = next_state[:, 0] + next_state[:, 2]
        next_state[:, 1] = next_state[:, 1] + next_state[:, 3]

        return next_state.unsqueeze(0)

