from turtlebot_gym.turtlebot_gym.envs.utils.agent import Agent
from turtlebot_gym.turtlebot_gym.envs.utils.state import JointState


class Robot(Agent):
    def __init__(self, config, section):
        super().__init__(config, section)

    def act(self, ob, obstacle):
        if self.policy is None:
            raise AttributeError('Policy attribute has to be set!')

        state = JointState(self.get_full_state(), ob)
        action = self.policy.predict(state, obstacle)
        return action
