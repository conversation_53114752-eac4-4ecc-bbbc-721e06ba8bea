import numpy as np
import math


def point_to_segment_dist(x1, y1, x2, y2, x3, y3):
    """
    Calculate the closest distance between point(x3, y3) and a line segment with two endpoints (x1, y1), (x2, y2)

    """
    px = x2 - x1
    py = y2 - y1

    if px == 0 and py == 0:
        return x3-x1, y3-y1, np.linalg.norm((x3-x1, y3-y1))

    u = ((x3 - x1) * px + (y3 - y1) * py) / (px * px + py * py)

    if u > 1:
        u = 1
    elif u < 0:
        u = 0

    # (x, y) is the closest point to (x3, y3) on the line segment
    x = x1 + u * px
    y = y1 + u * py

    return x - x3, y-y3, np.linalg.norm((x - x3, y-y3))

def point_to_cos(x1, y1, x2, y2):
    # if (x1 * x1 + y1 * y1)==0 or (x2 * x2 + y2 * y2)==0:
    #     print(x1, y1, x2, y2)
    va = int((x1 * x2 + y1 * y2)/ math.sqrt(x1 * x1 + y1 * y1)/ math.sqrt(x2 * x2 + y2 * y2)*1000000)/1000000
    # if va > 1 or va < -1:
    #     print(x1, y1, x2, y2)
    return math.acos(va)