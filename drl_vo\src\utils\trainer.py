import logging
import abc
import copy
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import rospy
import numpy as np
import math


class MPRLTrainer(object):
    def __init__(self, human_vector_predictor, value_estimator, state_predictor, memory, device, policy, writer, batch_size, optimizer_str, human_num,
                 reduce_sp_update_frequency, freeze_state_predictor, detach_state_predictor, share_graph_model):
        """
        Train the trainable model of a policy
        """
        self.human_vector_predictor = human_vector_predictor
        self.value_estimator = value_estimator
        self.state_predictor = state_predictor
        self.device = device
        self.writer = writer
        self.target_policy = policy
        self.target_model = None
        self.criterion = nn.MSELoss().to(device)
        self.vec_criterion = vec_pre_loss()
        self.memory = memory
        self.data_loader = None
        self.batch_size = batch_size
        self.optimizer_str = optimizer_str
        self.reduce_sp_update_frequency = reduce_sp_update_frequency
        self.state_predictor_update_interval = human_num
        self.freeze_state_predictor = freeze_state_predictor
        self.detach_state_predictor = detach_state_predictor
        self.share_graph_model = share_graph_model
        self.v_optimizer = None
        self.s_optimizer = None
        self.h_optimizer = None
        self.init_h_para = None
        self.init_s_para = None
        self.losses_s_q = None
        self.losses_h_q = None
        self.init_flag = False

        # for value update
        self.gamma = 0.9
        self.time_step = 0.05
        self.v_pref = 0.5
        self.update_step = 5
        self.human_num = 10
        #和learning_rate保持一致
        self.update_lr = 0.00001
        self.t_n = None

    def update_target_model(self, target_model):
        self.target_model = copy.deepcopy(target_model)

    def set_learning_rate(self, learning_rate):
        if self.optimizer_str == 'Adam':
            self.v_optimizer = optim.Adam(self.value_estimator.parameters(), lr=learning_rate)
            self.h_optimizer = optim.Adam(self.human_vector_predictor.parameters(), lr=learning_rate)
            if self.state_predictor.trainable:
                self.s_optimizer = optim.Adam(self.state_predictor.parameters(), lr=learning_rate)
        elif self.optimizer_str == 'SGD':
            self.v_optimizer = optim.SGD(self.value_estimator.parameters(), lr=learning_rate, momentum=0.9)
            self.h_optimizer = optim.SGD(self.human_vector_predictor.parameters(), lr=learning_rate)
            if self.state_predictor.trainable:
                self.s_optimizer = optim.SGD(self.state_predictor.parameters(), lr=learning_rate)
        else:
            raise NotImplementedError

        if self.state_predictor.trainable:
            logging.info('Lr: {} for parameters {} with {} optimizer'.format(learning_rate, ' '.join(
                [name for name, param in list(self.value_estimator.named_parameters()) +
                 list(self.state_predictor.named_parameters())]), self.optimizer_str))
        else:
            logging.info('Lr: {} for parameters {} with {} optimizer'.format(learning_rate, ' '.join(
                [name for name, param in list(self.value_estimator.named_parameters())]), self.optimizer_str))

    def optimize_epoch(self, num_epochs):
        if self.v_optimizer is None:
            raise ValueError('Learning rate is not set!')
        if self.data_loader is None:
            self.data_loader = DataLoader(self.memory, self.batch_size, shuffle=False)

        for epoch in range(num_epochs):
            epoch_v_loss = 0
            epoch_s_loss = 0
            logging.debug('{}-th epoch starts'.format(epoch))

            update_counter = 0
            for data in self.data_loader:
                robot_states, human_states, values, _, _, next_human_states = data

                # optimize value estimator
                self.v_optimizer.zero_grad()
                outputs = self.value_estimator((robot_states, human_states))
                values = values.to(self.device)
                loss = self.criterion(outputs, values)
                loss.backward()
                self.v_optimizer.step()
                epoch_v_loss += loss.data.item()

                # optimize state predictor
                if self.state_predictor.trainable:
                    update_state_predictor = True
                    if update_counter % self.state_predictor_update_interval != 0:
                        update_state_predictor = False

                    if update_state_predictor:
                        self.s_optimizer.zero_grad()
                        _, next_human_states_est = self.state_predictor((robot_states, human_states), None)
                        loss = self.criterion(next_human_states_est, next_human_states)
                        loss.backward()
                        self.s_optimizer.step()
                        epoch_s_loss += loss.data.item()
                    update_counter += 1

            logging.debug('{}-th epoch ends'.format(epoch))
            self.writer.add_scalar('IL/epoch_v_loss', epoch_v_loss / len(self.memory), epoch)
            self.writer.add_scalar('IL/epoch_s_loss', epoch_s_loss / len(self.memory), epoch)
            logging.info('Average loss in epoch %d: %.2E, %.2E', epoch, epoch_v_loss / len(self.memory),
                         epoch_s_loss / len(self.memory))

        return

    def optimize_batch(self, num_batches, episode, task_num):
        if task_num == 0:
            self.t_n = -1

        #最开始的时候可能因为数据不够所以导致一些任务不够开始训练的
        if len(self.memory[task_num]) < self.batch_size:
            rospy.loginfo("data not enough")
            return
        else:
            self.t_n = self.t_n + 1
        if self.v_optimizer is None:
            raise ValueError('Learning rate is not set!')
        if self.data_loader is None:
            # print("memory: "+str(len(self.memory[task_num]))+"batch size:"+str(self.batch_size))
            self.data_loader = DataLoader(self.memory[task_num], self.batch_size, shuffle=True)
        v_losses = 0
        s_losses = 0
        h_losses = 0
        batch_count = 0
        
        if self.t_n == 0:
            self.init_h_para = self.human_vector_predictor.parameters()
            self.losses_h_q = list()
            self.init_s_para = self.state_predictor.parameters()
            self.losses_s_q = list()
            self.init_flag = True
        else:
            self.human_vector_predictor.set_parameters(self.init_h_para)
            self.state_predictor.set_parameters(self.init_s_para)
        for data in self.data_loader:
            # print(data.shape)
            robot_states, human_states, _, rewards, next_robot_states, next_human_states = data
            # print(robot_states.shape)
            # print(human_states.shape)

            # optimize value estimator
            self.v_optimizer.zero_grad()
            outputs = self.value_estimator((robot_states, human_states))

            gamma_bar = pow(self.gamma, self.time_step * self.v_pref)
            target_values = rewards + gamma_bar * self.target_model((next_robot_states, next_human_states))

            # values = values.to(self.device)
            loss = self.criterion(outputs, target_values)
            loss.backward()
            self.v_optimizer.step()
            v_losses += loss.data.item()

            # optimize vector predictor
            rg = 2
            # h_count = 0
            # for k in range(self.update_step):
            #下面这个for循环可以替代self.update_step次数的更新
            all_human = list()
            all_human_temp = list()
            # all_human_i = list()
            all_human_target = list()
            #处理不同时刻的行人数据
            human_num = 0
            for k, time_human_states in enumerate(human_states):
                if np.random.random() > 0.7:
                    continue
                # all_human = list()
                # all_human_temp = list()
                # all_human_i = list()
                for i, human_i in enumerate(time_human_states):
                    if human_num > self.human_num:
                        break
                    human_temp = list()
                    for human_j in time_human_states:
                        if (human_j[0]-human_i[0]) ** 2 + (human_j[1]-human_i[1]) ** 2 <= rg ** 2:
                            human_cur = [human_j[0]-human_i[0], human_j[1]-human_i[1], human_j[2], human_j[3]]
                            human_temp.append(human_cur) 
                    if len(human_temp) > 1:
                        human_num = human_num + 1
                        all_human_temp.append(len(human_temp))
                        human_temp = torch.tensor(human_temp).numpy()
                        #这里保证每一个human_temp元素数量都是一样的
                        human_temp = self.random_sample(human_temp, 33)
                        all_human.append(torch.from_numpy(human_temp))
                        all_human_target.append(human_i)
                        # all_human_i.append([k, i])

            all_human_target = torch.stack(all_human_target)
            all_human = torch.stack(all_human).to("cuda:0")
            all_human_temp = torch.tensor(all_human_temp)
            #将all_human, all_human_temp, all_human_i分为支持集和查询集,div是集合数量的一半，用来做分界点
            div = int(len(all_human) / 2)
            spt_all_human = all_human[:div]
            qry_all_human = all_human[div:]
            apt_all_human_temp = all_human_temp[:div]
            qry_all_human_temp = all_human_temp[div:]
            # apt_all_human_i = all_human_i[:div]
            # qry_all_human_i = all_human_i[div:]
            apt_all_human_target = all_human_target[:div]
            qry_all_human_target = all_human_target[div:]
            for k in range(self.update_step):
                if k == 0:
                    vec_pre = self.human_vector_predictor(spt_all_human, vars=None)
                    # vec_pre = self.human_vector_predictor(human_temp.expand(1,-1,-1))
                    # print("shape: time_human_states:", time_human_states.shape, "all_human:", all_human.shape, "vec_pre:", vec_pre.shape)
                    loss = self.vec_criterion(spt_all_human, vec_pre, apt_all_human_target, apt_all_human_temp)
                    grad = torch.autograd.grad(loss, self.human_vector_predictor.parameters())
                    fast_weights = list(map(lambda p: p[1] - self.update_lr * p[0], zip(grad, self.human_vector_predictor.parameters())))
                else:
                    vec_pre = self.human_vector_predictor(spt_all_human, vars=fast_weights)
                    loss = self.vec_criterion(spt_all_human, vec_pre, apt_all_human_target, apt_all_human_temp)
                    grad = torch.autograd.grad(loss, self.human_vector_predictor.parameters())
                    fast_weights = list(map(lambda p: p[1] - self.update_lr * p[0], zip(grad, fast_weights)))

                if k == self.update_step - 1:
                    vec_pre = self.human_vector_predictor(qry_all_human, vars=fast_weights)
                    loss = self.vec_criterion(qry_all_human, vec_pre, qry_all_human_target, qry_all_human_temp)
                    if len(self.losses_h_q) < self.t_n + 1:
                        self.losses_h_q.append(loss)
                    else:
                        self.losses_h_q[self.t_n] =loss

            self.h_optimizer.zero_grad()
            loss = sum(self.losses_h_q)/len(self.losses_h_q)
            loss.backward()
            self.h_optimizer.step()
            self.losses_h_q[self.t_n] = self.losses_h_q[self.t_n].detach()
            h_losses += loss.data.item()
            
            # outputs = self.human_vector_predictor()


            # optimize state predictor
            if self.state_predictor.trainable:
                update_state_predictor = True
                if self.freeze_state_predictor:
                    update_state_predictor = False
                elif self.reduce_sp_update_frequency and batch_count % self.state_predictor_update_interval == 0:
                    update_state_predictor = False

                if update_state_predictor:
                    #划分支持集和查询集
                    div = int(len(robot_states) / 2)
                    spt_robot_states = robot_states[:div]
                    qry_robot_states = robot_states[div:]
                    spt_human_states = human_states[:div]
                    qry_human_states = human_states[div:]
                    spt_next_human_states = next_human_states[:div]
                    qry_next_human_states = next_human_states[div:]

                    for k in range(self.update_step):
                        #用支持集更新网络参数
                        if k == 0:
                            _, next_human_states_est = self.state_predictor((spt_robot_states, spt_human_states), None, vars=None,
                                                                    detach=self.detach_state_predictor)
                            # print(next_human_states_est, spt_next_human_states)
                            loss = self.criterion(next_human_states_est, spt_next_human_states)
                            grad = torch.autograd.grad(loss, self.state_predictor.parameters())
                            fast_weights = list(map(lambda p: p[1] - self.update_lr * p[0], zip(grad, self.state_predictor.parameters())))
                        else:
                            _, next_human_states_est = self.state_predictor((spt_robot_states, spt_human_states), None, vars=fast_weights,
                                                                    detach=self.detach_state_predictor)
                            loss = self.criterion(next_human_states_est, spt_next_human_states)
                            grad = torch.autograd.grad(loss, self.state_predictor.parameters())
                            fast_weights = list(map(lambda p: p[1] - self.update_lr * p[0], zip(grad, fast_weights)))

                        #用查询集在支持集更新的网络参数上计算损失
                        if k == self.update_step - 1:
                            _, next_human_states_est = self.state_predictor((qry_robot_states, qry_human_states), None, vars=fast_weights,
                                                                    detach=self.detach_state_predictor)
                            loss = self.criterion(next_human_states_est, qry_next_human_states)
                            # print(next_human_states_est, qry_next_human_states)
                            # print(loss)
                            if len(self.losses_s_q) < self.t_n + 1:
                                self.losses_s_q.append(loss)
                            else:
                                self.losses_s_q[self.t_n] =loss
                    #用查询集上得到的损失更新神经网络
                    self.s_optimizer.zero_grad()
                    loss = sum(self.losses_s_q)/len(self.losses_s_q)
                    loss.backward()
                    self.s_optimizer.step()
                    self.losses_s_q[self.t_n] = self.losses_s_q[self.t_n].detach()
                    s_losses += loss.data.item()

            batch_count += 1
            if batch_count > num_batches:
                break

        average_v_loss = v_losses / num_batches
        average_s_loss = s_losses / num_batches
        average_h_loss = h_losses / num_batches
        rospy.loginfo('Average loss : %.2E, %.2E, %.2E', average_v_loss, average_s_loss, average_h_loss)
        # logging.info('Average loss : %.2E, %.2E, %.2E', average_v_loss, average_s_loss, average_h_loss)
        self.writer.add_scalar('RL/average_v_loss', average_v_loss, episode)
        self.writer.add_scalar('RL/average_s_loss', average_s_loss, episode)
        self.writer.add_scalar('RL/average_h_loss', average_h_loss, episode)

        return average_v_loss, average_s_loss

    def random_sample(self, pc, n):
        idx = np.random.permutation(pc.shape[0])
        if idx.shape[0] < n:
            idx = np.concatenate([np.random.randint(pc.shape[0], size=n-pc.shape[0]), idx])
        return pc[idx[:n]]



class VNRLTrainer(object):
    def __init__(self, model, memory, device, policy, batch_size, optimizer_str, writer):
        """
        Train the trainable model of a policy
        """
        self.model = model
        self.device = device
        self.policy = policy
        self.target_model = None
        self.criterion = nn.MSELoss().to(device)
        self.memory = memory
        self.data_loader = None
        self.batch_size = batch_size
        self.optimizer_str = optimizer_str
        self.optimizer = None
        self.writer = writer

        # for value update
        self.gamma = 0.9
        self.time_step = 0.25
        self.v_pref = 1

    def update_target_model(self, target_model):
        self.target_model = copy.deepcopy(target_model)

    def set_learning_rate(self, learning_rate):
        if self.optimizer_str == 'Adam':
            self.optimizer = optim.Adam(self.model.parameters(), lr=learning_rate)
        elif self.optimizer_str == 'SGD':
            self.optimizer = optim.SGD(self.model.parameters(), lr=learning_rate, momentum=0.9)
        else:
            raise NotImplementedError
        logging.info('Lr: {} for parameters {} with {} optimizer'.format(learning_rate, ' '.join(
            [name for name, param in self.model.named_parameters()]), self.optimizer_str))

    def optimize_epoch(self, num_epochs):
        if self.optimizer is None:
            raise ValueError('Learning rate is not set!')
        if self.data_loader is None:
            self.data_loader = DataLoader(self.memory, self.batch_size, shuffle=True, collate_fn=pad_batch)
        average_epoch_loss = 0
        for epoch in range(num_epochs):
            epoch_loss = 0
            logging.debug('{}-th epoch starts'.format(epoch))
            for data in self.data_loader:
                inputs, values, _, _ = data
                self.optimizer.zero_grad()
                outputs = self.model(inputs)
                values = values.to(self.device)
                loss = self.criterion(outputs, values)
                loss.backward()
                self.optimizer.step()
                epoch_loss += loss.data.item()
            logging.debug('{}-th epoch ends'.format(epoch))
            average_epoch_loss = epoch_loss / len(self.memory)
            self.writer.add_scalar('IL/average_epoch_loss', average_epoch_loss, epoch)
            logging.info('Average loss in epoch %d: %.2E', epoch, average_epoch_loss)

        return average_epoch_loss

    def optimize_batch(self, num_batches, episode=None):
        if self.optimizer is None:
            raise ValueError('Learning rate is not set!')
        if self.data_loader is None:
            self.data_loader = DataLoader(self.memory, self.batch_size, shuffle=True, collate_fn=pad_batch)
        losses = 0
        batch_count = 0
        for data in self.data_loader:
            inputs, _, rewards, next_states = data
            self.optimizer.zero_grad()
            outputs = self.model(inputs)

            gamma_bar = pow(self.gamma, self.time_step * self.v_pref)
            target_values = rewards + gamma_bar * self.target_model(next_states)

            loss = self.criterion(outputs, target_values)
            loss.backward()
            self.optimizer.step()
            losses += loss.data.item()
            batch_count += 1
            if batch_count > num_batches:
                break

        average_loss = losses / num_batches
        logging.info('Average loss : %.2E', average_loss)

        return average_loss


def pad_batch(batch):
    """
    args:
        batch - list of (tensor, label)
    return:
        xs - a tensor of all examples in 'batch' after padding
        ys - a LongTensor of all labels in batch
    """
    def sort_states(position):
        # sort the sequences in the decreasing order of length
        sequences = sorted([x[position] for x in batch], reverse=True, key=lambda t: t.size()[0])
        packed_sequences = torch.nn.utils.rnn.pack_sequence(sequences)
        return torch.nn.utils.rnn.pad_packed_sequence(packed_sequences, batch_first=True)

    states = sort_states(0)
    values = torch.cat([x[1] for x in batch]).unsqueeze(1)
    rewards = torch.cat([x[2] for x in batch]).unsqueeze(1)
    next_states = sort_states(3)

    return states, values, rewards, next_states

class vec_pre_loss(nn.Module):
    def __init__(self):
        super().__init__()
        
    def forward(self, all_human, vec_pre, all_human_target, all_human_temp):
        #第一个损失函数是用余弦相似度来评价，大小是从1到-1，余弦相似度越大越好
        loss_0 = (vec_pre[:, 0] * all_human_target[:, 2] + vec_pre[:, 1] * all_human_target[:, 3]) / torch.sqrt(vec_pre[:, 0] ** 2 + vec_pre[:, 1] ** 2) / torch.sqrt(all_human_target[:, 2] ** 2 + all_human_target[:, 3] ** 2) * len(all_human_temp) /50
        loss_0 = torch.mean(loss_0)
        # print("loss_0:", loss_0)
        loss_1 = 0
        for i in range(all_human.shape[0]):
            loss_t = 0
            for j in range(all_human.shape[1]):
                #去掉重复项
                if j >= all_human_temp[i]:
                    break
                loss_t_t = 0
                if all_human[i, j, 0] != 0:
                    loss_t_t = loss_t_t + (all_human[i, j, 2] - vec_pre[i, 0])/all_human[i, j, 0]
                    # print("dx:", all_human[i, j, 2] - vec_pre[i, 0], "x:", all_human[i, j, 0])
                if all_human[i, j, 1] != 0:
                    loss_t_t =  loss_t_t + (all_human[i, j, 3] - vec_pre[i, 1])/all_human[i, j, 1]
                    # print("dy:", all_human[i, j, 3] - vec_pre[i, 1], "x:", all_human[i, j, 1])
                # print("loss_t:", loss_t)
                # loss_t =  loss_t + (all_human[i, j, 2] - vec_pre[i, 0])/all_human[i, j, 0] + (all_human[i, j, 3] - vec_pre[i, 1])/all_human[i, j, 1]
                if loss_t_t < 0:
                    loss_t_t = 0 - loss_t_t
                # print(loss_t_t)
                loss_t += loss_t_t
            #100是超参数
            loss_1 = loss_1 + loss_t / all_human_temp[i] / 100

        #第二个损失函数是散度，假设场为无源场，所以散度越小越好
        loss_1 = loss_1 / all_human.shape[0]
        # print("loss_1:", loss_1)

        loss = 1 - loss_0 + loss_1

        return loss