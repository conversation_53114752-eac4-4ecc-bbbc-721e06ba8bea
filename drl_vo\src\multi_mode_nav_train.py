#!/usr/bin/env python

from geometry_msgs.msg import Twist
from geometry_msgs.msg import Point

import sys
import select
import termios
import tty
import roslaunch

import rospy
import sys
import logging
import argparse
import os
import shutil
import importlib.util
import torch
import gym
import copy
import git
import re
from tensorboardX import SummaryWriter
from turtlebot_gym.turtlebot_gym.envs.utils.robot import Robot
from utils.trainer import VNRLTrainer, MPRLTrainer
from utils.memory import ReplayMemory
from utils.explorer import Explorer
from policy.policy_factory import policy_factory
from turtlebot_gym.turtlebot_gym.envs.crowd_sim import CrowdSim 

def set_random_seeds(seed):
    """
    Sets the random seeds for pytorch cpu and gpu
    """
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    return None


rospy.init_node('pedsim_multimode_train')
# pub = rospy.Publisher('~cmd_vel', Twist, queue_size=5)
parser = argparse.ArgumentParser('Parse configuration file')
#parser.add_argument('--policy', type=str, default='model_predictive_rl')
parser.add_argument('--policy', type=str, default='RGL')
parser.add_argument('--config', type=str, default='configs/icra_benchmark/mp_separate.py')
parser.add_argument('--output_dir', type=str, default='data/output')
parser.add_argument('--overwrite', default=False, action='store_true')
parser.add_argument('--weights', type=str)
parser.add_argument('--resume', default=True, action='store_true')
parser.add_argument('--gpu', default=True, action='store_true')
parser.add_argument('--debug', default=False, action='store_true')
parser.add_argument('--test_after_every_eval', default=False, action='store_true')
parser.add_argument('--randomseed', type=int, default=17)
# parser.add_argument('--ep', type=int, default=0)
ep = rospy.get_param('~ep', 0) * 50

sys_args = parser.parse_args(args=[])
set_random_seeds(sys_args.randomseed)
# configure paths
make_new_dir = True
abs_path = "/home/<USER>/catkin_wd/src/drl_vo_nav/drl_vo/src"
# ep = ep * 5
# print("ep", ep)
if ep==0:
    sys_args.resume = False
else:
    sys_args.resume = True
if os.path.exists(os.path.join(abs_path, sys_args.output_dir)):
    if sys_args.overwrite:
        shutil.rmtree(sys_args.output_dir)
    else:
        # key = input('Output directory already exists! Overwrite the folder? (y/n)')
        key = 'n'
        if key == 'y' and not sys_args.resume:
            shutil.rmtree(os.path.join(abs_path, sys_args.output_dir))
        else:
            make_new_dir = False
if make_new_dir:
    os.makedirs(os.path.join(abs_path, sys_args.output_dir))
    shutil.copy(os.path.join(abs_path, sys_args.config), os.path.join(abs_path, os.path.join(sys_args.output_dir, 'config.py')))

sys_args.config = os.path.join(abs_path, os.path.join(sys_args.output_dir, 'config.py'))
log_file = os.path.join(abs_path, os.path.join(sys_args.output_dir, 'output.log'))
up_weight_file = os.path.join(abs_path, os.path.join(sys_args.output_dir, 'up_model.pth'))
rl_weight_file = os.path.join(abs_path, os.path.join(sys_args.output_dir, 'rl_model_4.pth'))

spec = importlib.util.spec_from_file_location('config', sys_args.config)
if spec is None:
    parser.error('Config file not found.')
config = importlib.util.module_from_spec(spec)
spec.loader.exec_module(config)

# configure logging
mode = 'a' if sys_args.resume else 'w'
file_handler = logging.FileHandler(log_file, mode=mode)
stdout_handler = logging.StreamHandler(sys.stdout)
level = logging.INFO if not sys_args.debug else logging.DEBUG
logging.basicConfig(level=level, handlers=[stdout_handler, file_handler],
                    format='%(asctime)s, %(levelname)s: %(message)s', datefmt="%Y-%m-%d %H:%M:%S")
#向代码仓库里面提交代码
#repo = git.Repo(search_parent_directories=True)
#logging.info('Current git head hash code: {}'.format(repo.head.object.hexsha))
rospy.loginfo('Current config content is :{}'.format(config))
device = torch.device("cuda:0" if torch.cuda.is_available() and sys_args.gpu else "cpu")
rospy.loginfo('Using device: %s', device)
writer = SummaryWriter(log_dir=sys_args.output_dir)
#print(os.path.join(abs_path, args.output_dir))

# configure policy
policy_config = config.PolicyConfig()
policy = policy_factory[policy_config.name]()
if not policy.trainable:
    parser.error('Policy has to be trainable')
#print(os.path.join(abs_path, args.output_dir))
policy.configure(policy_config)
policy.set_device(device)

# configure environment
env_config = config.EnvConfig(sys_args.debug)
env = gym.make('CrowdSim-v0')
#env = CrowdSim()
env.configure(env_config, policy.human_vector_predictor)
robot = Robot(env_config, 'robot')
robot.time_step = env.time_step
env.set_robot(robot)

# read training parameters
train_config = config.TrainConfig(sys_args.debug)
rl_learning_rate = train_config.train.rl_learning_rate
train_batches = train_config.train.train_batches
train_episodes = train_config.train.train_episodes
sample_episodes = train_config.train.sample_episodes
target_update_interval = train_config.train.target_update_interval
evaluation_interval = train_config.train.evaluation_interval
capacity = train_config.train.capacity
epsilon_start = train_config.train.epsilon_start
epsilon_end = train_config.train.epsilon_end
epsilon_decay = train_config.train.epsilon_decay
checkpoint_interval = train_config.train.checkpoint_interval
over_task_num = 2
eva = False

# configure trainer and explorer
# memory = ReplayMemory(capacity)
# 每种行为模式下的训练数据保存在不同的memory数据结构中
multi_memory = list()
for i in range(over_task_num):
    multi_memory.append(ReplayMemory(capacity))

if os.path.exists(up_weight_file):
    policy.load_state_dict(torch.load(up_weight_file))

model_0 = copy.deepcopy(policy.get_model())

if sys_args.resume:
        if not os.path.exists(rl_weight_file):
            logging.error('RL weights does not exist')
        policy.load_state_dict(torch.load(rl_weight_file))
        # rl_weight_file = os.path.join(sys_args.output_dir, 'resumed_rl_model.pth')
        #换成绝对路径
        rl_weight_file = os.path.join(abs_path, os.path.join(sys_args.output_dir, 'rl_model.pth'))
        rospy.loginfo('Load reinforcement learning trained weights. Resume training')
rl_weight_file = os.path.join(abs_path, os.path.join(sys_args.output_dir, 'rl_model.pth'))

model = policy.get_model()
batch_size = train_config.trainer.batch_size
optimizer = train_config.trainer.optimizer
if policy_config.name == 'model_predictive_rl':
    trainer = MPRLTrainer(policy.human_vector_predictor, model, policy.state_predictor, multi_memory, device, policy, writer, batch_size, optimizer, env.human_num,
                          reduce_sp_update_frequency=train_config.train.reduce_sp_update_frequency,
                          freeze_state_predictor=train_config.train.freeze_state_predictor,
                          detach_state_predictor=train_config.train.detach_state_predictor,
                          share_graph_model=policy_config.model_predictive_rl.share_graph_model)
else:
    trainer = VNRLTrainerSummaryWriter(model, memory, device, policy, batch_size, optimizer, writer)
explorer = Explorer(env, robot, device, writer, multi_memory, policy.gamma, target_policy=policy)

trainer.update_target_model(model_0)

# reinforcement learning
policy.set_env(env)
robot.set_policy(policy)
print(policy.kinematics)
robot.print_info()
trainer.set_learning_rate(rl_learning_rate)
# fill the memory pool with some RL experience
# if sys_args.resume:
#     robot.policy.set_epsilon(epsilon_end)
#     explorer.run_k_episodes(100, 'train', update_memory=True, episode=0)
#     logging.info('Experience set size: %d/%d', len(memory), memory.capacity)
episode = 0
best_val_reward = -1
best_val_model = None
# evaluate the model after imitation learning
if eva:
    env.shut_simulation()
    env.start_simulation(8)
    logging.info('Evaluate the model instantly after imitation learning on the validation cases')
    explorer.run_k_episodes(env.case_size['val'], 'val', 8,  episode=episode)
    env.shut_simulation()
    explorer.log('val', episode // evaluation_interval)

    # if args.test_after_every_eval:
    #     explorer.run_k_episodes(env.case_size['test'], 'test', episode=episode, print_failure=True)
    #     explorer.log('test', episode // evaluation_interval)

# episode = 0
while episode < train_episodes:
    if sys_args.resume:
        epsilon = epsilon_end
    else:
        if episode < epsilon_decay:
            epsilon = epsilon_start + (epsilon_end - epsilon_start) / epsilon_decay * episode
        else:
            epsilon = epsilon_end
    robot.policy.set_epsilon(epsilon)

    # sample k episodes into memory and optimize over the generated memory
    for task_num in range(over_task_num):
        print("ep:", ep)
        if task_num != 0 or episode != 0:
            # env.__init__()
            # env.configure(env_config)
            # env.set_robot(robot)
            # 这里将来传入task_num参数之后，选择要打开的环境
            env.start_simulation(task_num)
        explorer.run_k_episodes(sample_episodes, 'train', task_num, update_memory=True, episode=episode + ep)
        explorer.log('train', episode+ep)
        env.shut_simulation()
        trainer.optimize_batch(train_batches, episode, task_num)
    episode += 1

    if ep != 0 and (episode + ep - 1) % target_update_interval == 0:
        trainer.update_target_model(model)
        current_checkpoint = (episode + ep - 1) // target_update_interval - 1
        save_every_checkpoint_rl_weight_file = up_weight_file.split('.')[0] + '_' + str(current_checkpoint) + '.pth'
        policy.save_model(save_every_checkpoint_rl_weight_file)
        save_every_checkpoint_rl_weight_file = up_weight_file.split('.')[0] + '.pth'
        policy.save_model(save_every_checkpoint_rl_weight_file)
        model_0 = copy.deepcopy(model)
    # evaluate the model
    if ep != 0 and ep % evaluation_interval == 0 and episode == 1:
        env.start_simulation(8)
        _, _, _, reward, _ = explorer.run_k_episodes(env.case_size['val'], 'val', task_num=8, episode=episode)
        explorer.log('val', episode // evaluation_interval)
        env.shut_simulation()

        if ep % checkpoint_interval == 0 and reward > best_val_reward:
            best_val_reward = reward
            best_val_model = copy.deepcopy(policy.get_state_dict())
    # test after every evaluation to check how the generalization performance evolves
        if sys_args.test_after_every_eval:
            explorer.run_k_episodes(env.case_size['test'], 'test', episode=episode, print_failure=True)
            explorer.log('test', episode // evaluation_interval)

    if episode != 0 and episode % checkpoint_interval == 0:
        current_checkpoint = episode // checkpoint_interval - 1
        save_every_checkpoint_rl_weight_file = rl_weight_file.split('.')[0] + '_' + str(current_checkpoint) + '.pth'
        # print(save_every_checkpoint_rl_weight_file)
        policy.save_model(save_every_checkpoint_rl_weight_file)


# # test with the best val model
if best_val_model is not None:
    policy.load_state_dict(best_val_model)
    torch.save(best_val_model, os.path.join(sys_args.output_dir, 'best_val.pth'))
    logging.info('Save the best val model with the reward: {}'.format(best_val_reward))
# explorer.run_k_episodes(env.case_size['test'], 'test', task_num=8, episode=episode + ep, print_failure=True)

# if __name__ == '__main__':
#     rospy.init_node('pedsim_multimode_train')
#     pub = rospy.Publisher('~cmd_vel', Twist, queue_size=5)
#     parser = argparse.ArgumentParser('Parse configuration file')
#     #parser.add_argument('--policy', type=str, default='model_predictive_rl')
#     parser.add_argument('--policy', type=str, default='RGL')
#     parser.add_argument('--config', type=str, default='configs/icra_benchmark/mp_separate.py')
#     parser.add_argument('--output_dir', type=str, default='data/output')
#     parser.add_argument('--overwrite', default=False, action='store_true')
#     parser.add_argument('--weights', type=str)
#     parser.add_argument('--resume', default=False, action='store_true')
#     parser.add_argument('--gpu', default=True, action='store_true')
#     parser.add_argument('--debug', default=False, action='store_true')
#     parser.add_argument('--test_after_every_eval', default=True, action='store_true')
#     parser.add_argument('--randomseed', type=int, default=17)

#     # arguments for GCN
#     # parser.add_argument('--X_dim', type=int, default=32)
#     # parser.add_argument('--layers', type=int, default=2)
#     # parser.add_argument('--sim_func', type=str, default='embedded_gaussian')
#     # parser.add_argument('--layerwise_graph', default=False, action='store_true')
#     # parser.add_argument('--skip_connection', default=True, action='store_true')

#     sys_args = parser.parse_args(args=[])

#     main(sys_args)





