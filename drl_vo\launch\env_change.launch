<launch>
  <arg name="gui" default="true" doc="Bring up the Gazebo graphical interface"/>
  <arg name="default_queue_size" default="10"/>
  <arg name="max_robot_speed" default="1.5"/>
  <arg name="robot_mode" default="1"/>
  <arg name="enable_groups" default="true"/>
  <arg name="simulation_factor" default="1"/>
  <arg name="update_rate" default="25.0"/>
  <arg name="rviz" default="true"/> 
  <arg name="pose_initial_x" default="1.0"/> 
  <arg name="pose_initial_y" default="1.0"/>
  <arg name="pose_initial_theta" default="0.0"/>
  <arg name="initial_pose_x" default="0"/>
  <arg name="initial_pose_y" default="0"/>
  <arg name="initial_pose_a" default="0"/>
  <arg name="custom_amcl_launch_file" default="$(find robot_gazebo)/launch/includes/amcl/amcl.launch.xml"/> 

  <!-- CNN DATA -->
  <include file="$(find drl_vo_nav)/launch/nav_cnn_data.launch"/>

  <include file="$(arg custom_amcl_launch_file)">
    <arg name="initial_pose_x" value="$(arg initial_pose_x)"/>
    <arg name="initial_pose_y" value="$(arg initial_pose_y)"/>
    <arg name="initial_pose_a" value="$(arg initial_pose_a)"/>
  </include>


  <!-- Gazebo -->
  <!--include file="$(find pedsim_gazebo_plugin)/launch/eng_hall.launch"/-->
  <include file="$(find gazebo_ros)/launch/empty_world.launch">
      <arg name="world_name" value="$(find pedsim_gazebo_plugin)/worlds/eng_hall.world"/>
      <arg name="gui" value="$(arg gui)" />
  </include>

  <!-- main simulator node -->
  <node name="pedsim_simulator" pkg="pedsim_simulator" type="pedsim_simulator" output="screen">
    <param name="scene_file" value="$(find pedsim_simulator)/scenarios/eng_hall.xml" type="string"/>
    <param name="default_queue_size" value="$(arg default_queue_size)" type="int"/>
    <param name="max_robot_speed" value="$(arg max_robot_speed)" type="double"/>
    <param name="robot_mode" value="$(arg robot_mode)" type="int"/>
    <param name="enable_groups" value="$(arg enable_groups)" type="bool"/>
    <param name="simulation_factor" value="$(arg simulation_factor)" type="double"/>
    <param name="update_rate" value="$(arg update_rate)" type="double"/>
    <param name="frame_id"  value="gazebo" />
  </node>

  <!-- Turtlebot -->
  <arg name="base"         value="$(optenv ROBOT_BASE kobuki)"/> <!-- create, roomba -->
  <arg name="battery"      value="$(optenv ROBOT_BATTERY /proc/acpi/battery/BAT0)"/>  <!-- /proc/acpi/battery/BAT0 -->
  <!--arg name="gui"          default="true"/-->
  <arg name="stacks"       value="$(optenv ROBOT_STACKS hexagons)"/>  <!-- circles, hexagons -->
  <arg name="laser_sensor" value="$(optenv ROBOT_LASER_SENSOR hokuyo_kinect)"/>  <!-- laser, hokuyo lidar -->
  <arg name="3d_sensor"    value="$(optenv TURTLEBOT_3D_SENSOR kinect)"/>  <!-- kinect, asus_xtion_pro -->

  <include file="$(find robot_gazebo)/launch/includes/$(arg base).launch.xml">
      <arg name="base" value="$(arg base)"/>
      <arg name="stacks" value="$(arg stacks)"/>
      <arg name="3d_sensor" value="$(arg 3d_sensor)"/>
      <arg name="laser_sensor" value="$(arg laser_sensor)"/>
      <arg name="initial_pose" value="-x $(arg pose_initial_x) -y $(arg pose_initial_y) -Y $(arg pose_initial_theta)"/>
  </include>

  <node name="map_server" pkg="map_server" type="map_server" args="$(find robot_gazebo)/maps/gazebo_eng_lobby/eng.yaml" />

  <include file="$(find robot_gazebo)/launch/view_navigation.launch" if="$(arg rviz)"/>
</launch>

