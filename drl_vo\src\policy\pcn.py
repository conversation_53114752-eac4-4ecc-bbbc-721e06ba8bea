import torch
import torch.nn as nn
from torch.nn import functional as F


class PCN(nn.Module):
    """
    "PCN: Point Cloud Completion Network"
    (https://arxiv.org/pdf/1808.00671.pdf)

    Attributes:
        num_dense:  16384
        latent_dim: 1024
        grid_size:  4
        num_coarse: 1024
    """

    def __init__(self, num_dense=16384, latent_dim=1024, grid_size=4):
        super().__init__()

        self.num_dense = num_dense
        self.latent_dim = latent_dim
        self.grid_size = grid_size
        self.vars = nn.ParameterList()

        assert self.num_dense % self.grid_size ** 2 == 0

        self.num_coarse = self.num_dense // (self.grid_size ** 2)

        self.first_conv = nn.Sequential(
            nn.Conv1d(4, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU(inplace=True),
            nn.Conv1d(128, 256, 1)
        )

        self.second_conv = nn.Sequential(
            nn.Conv1d(512, 512, 1),
            nn.BatchNorm1d(512),
            nn.ReLU(inplace=True),
            nn.Conv1d(512, self.latent_dim, 1)
        )

        self.mlp = nn.Sequential(
            nn.Linear(self.latent_dim, 1024),
            nn.ReLU(inplace=True),
            nn.Linear(1024, 1024),
            nn.ReLU(inplace=True),
            nn.Linear(1024, 2)
        )

        # self.final_conv = nn.Sequential(
        #     nn.Conv1d(1024 + 3 + 2, 512, 1),
        #     nn.BatchNorm1d(512),
        #     nn.ReLU(inplace=True),
        #     nn.Conv1d(512, 512, 1),
        #     nn.BatchNorm1d(512),
        #     nn.ReLU(inplace=True),
        #     nn.Conv1d(512, 3, 1)
        # )
        # a = torch.linspace(-0.05, 0.05, steps=self.grid_size, dtype=torch.float).view(1, self.grid_size).expand(self.grid_size, self.grid_size).reshape(1, -1)
        # b = torch.linspace(-0.05, 0.05, steps=self.grid_size, dtype=torch.float).view(self.grid_size, 1).expand(self.grid_size, self.grid_size).reshape(1, -1)
        
        # self.folding_seed = torch.cat([a, b], dim=0).view(1, 2, self.grid_size ** 2).cuda()  # (1, 2, S)

    def forward(self, xyz, vars=None):
        B, N, _ = xyz.shape
        
        # encoder
        feature = self.first_conv(xyz.transpose(2, 1))                                       # (B,  256, N)
        feature_global = torch.max(feature, dim=2, keepdim=True)[0]                          # (B,  256, 1)
        feature = torch.cat([feature_global.expand(-1, -1, N), feature], dim=1)              # (B,  512, N)
        feature = self.second_conv(feature)                                                  # (B, 1024, N)
        feature_global = torch.max(feature,dim=2,keepdim=False)[0]                           # (B, 1024)
        
        # decoder
        coarse = self.mlp(feature_global)                                                      # (B, 2), linear velocity and angular velocity
        # point_feat = coarse.unsqueeze(2).expand(-1, -1, self.grid_size ** 2, -1)             # (B, num_coarse, S, 3)
        # point_feat = point_feat.reshape(-1, self.num_dense, 3).transpose(2, 1)               # (B, 3, num_fine)

        # seed = self.folding_seed.unsqueeze(2).expand(B, -1, self.num_coarse, -1)             # (B, 2, num_coarse, S)
        # seed = seed.reshape(B, -1, self.num_dense)                                           # (B, 2, num_fine)

        # feature_global = feature_global.unsqueeze(2).expand(-1, -1, self.num_dense)          # (B, 1024, num_fine)
        # feat = torch.cat([feature_global, seed, point_feat], dim=1)                          # (B, 1024+2+3, num_fine)
    
        # fine = self.final_conv(feat) + point_feat                                            # (B, 3, num_fine), fine point cloud

        return coarse.contiguous()

class PCN_maml(nn.Module):
    """
    "PCN: Point Cloud Completion Network"
    (https://arxiv.org/pdf/1808.00671.pdf)

    Attributes:
        num_dense:  16384
        latent_dim: 1024
        grid_size:  4
        num_coarse: 1024
    """

    def __init__(self,latent_dim=64, grid_size=4):
        super().__init__()

        # self.num_dense = num_dense
        self.latent_dim = latent_dim
        self.grid_size = grid_size
        self.vars = nn.ParameterList()

        # assert self.num_dense % self.grid_size ** 2 == 0

        # self.num_coarse = self.num_dense // (self.grid_size ** 2)

        # self.first_conv = nn.Sequential(
        #     nn.Conv1d(4, 128, 1),
        #     nn.BatchNorm1d(128),
        #     nn.ReLU(inplace=True),
        #     nn.Conv1d(128, 256, 1)
        # )
        # w = nn.Parameter(torch.randn([128, 4, 1]))
        w = nn.Linear(4, 32, 1).weight
        self.vars.append(w)
        # b = nn.Parameter(torch.randn(128))
        b = nn.Linear(4, 32, 1).bias
        self.vars.append(b)
        #nn.BatchNorm1d是一个归一化函数，没有权重参数，nn.Relu同理
        # self.m_0 = nn.BatchNorm1d(32)
        # self.m_0.cuda()
        # w = nn.Parameter(torch.randn([256, 128, 1]))
        w = nn.Linear(32, 64, 1).weight
        self.vars.append(w)
        # b = nn.Parameter(torch.randn(256))
        b = nn.Linear(32, 64, 1).bias
        self.vars.append(b)

        # self.second_conv = nn.Sequential(
        #     nn.Conv1d(512, 512, 1),
        #     nn.BatchNorm1d(512),
        #     nn.ReLU(inplace=True),
        #     nn.Conv1d(512, self.latent_dim, 1)
        # )
        # w = nn.Parameter(torch.randn([512, 512, 1]))
        w = nn.Linear(128, 128, 1).weight
        self.vars.append(w)
        # b = nn.Parameter(torch.randn(512))
        b = nn.Linear(128, 128, 1).bias
        self.vars.append(b)
        #nn.BatchNorm1d是一个归一化函数，没有权重参数，nn.Relu同理
        # self.m_1 = nn.BatchNorm1d(128)
        # self.m_1.cuda()
        # w = nn.Parameter(torch.randn([self.latent_dim, 512,1]))
        w = nn.Linear(128, self.latent_dim, 1).weight
        self.vars.append(w)
        # b = nn.Parameter(torch.randn(self.latent_dim))
        b = nn.Linear(128, self.latent_dim, 1).bias
        self.vars.append(b)

        # self.mlp = nn.Sequential(
        #     nn.Linear(self.latent_dim, 1024),
        #     nn.ReLU(inplace=True),
        #     nn.Linear(1024, 1024),
        #     nn.ReLU(inplace=True),
        #     nn.Linear(1024, 2)
        # )
        # w = nn.Parameter(torch.randn([1024, self.latent_dim]))
        w = nn.Linear(self.latent_dim, 64).weight
        self.vars.append(w)
        # b = nn.Parameter(torch.randn(1024))
        b = nn.Linear(self.latent_dim, 64).bias
        self.vars.append(b)
        #nn.BatchNorm1d是一个归一化函数，没有权重参数，nn.Relu同理
        # w = nn.Parameter(torch.randn([1024, 1024]))
        # w = nn.Linear(64, 64).weight
        # self.vars.append(w)
        # # b = nn.Parameter(torch.randn(1024))
        # b = nn.Linear(64, 64).bias
        # self.vars.append(b)
        # w = nn.Parameter(torch.randn([2, 1024]))
        w = nn.Linear(64, 2).weight
        self.vars.append(w)
        # b = nn.Parameter(torch.randn(2))
        b = nn.Linear(64, 2).bias
        self.vars.append(b)


        

    def forward(self, xyz, vars=None):
        B, N, X = xyz.shape
        # print(N, X)

        if vars is None:
            vars = self.vars

        idx = 0
        
        # encoder
        # feature = self.first_conv(xyz.transpose(2, 1))                                       # (B,  256, N)
        w, b = vars[idx], vars[idx + 1]
        x = F.linear(xyz, w, b)
        idx += 2
        # x = self.m_0(x)
        x = F.relu(x, inplace = True)
        w, b = vars[idx], vars[idx + 1]
        feature = F.linear(x, w, b)
        idx += 2

        feature_global = torch.max(feature.transpose(2, 1), dim=2, keepdim=True)[0]                          # (B,  256, 1)
        feature = torch.cat([feature_global.expand(-1, -1, N), feature.transpose(2, 1)], dim=1)              # (B,  512, N)
        
        # feature = self.second_conv(feature)                                                  # (B, 1024, N)
        w, b = vars[idx], vars[idx + 1]
        x = F.linear(feature.transpose(2, 1), w, b)
        idx += 2
        # x = self.m_1(x)
        x = F.relu(x, inplace = True)
        w, b = vars[idx], vars[idx + 1]
        feature = F.linear(x, w, b)
        idx += 2

        feature_global = torch.max(feature.transpose(2, 1),dim=2,keepdim=False)[0]                           # (B, 1024)
        
        # decoder
        # coarse = self.mlp(feature_global)                                                      # (B, 2), linear velocity and angular velocity
        w, b = vars[idx], vars[idx + 1]
        x = F.linear(feature_global, w, b)
        idx += 2
        x = F.relu(x, inplace = True)
        # w, b = vars[idx], vars[idx + 1]
        # x = F.linear(x, w, b)
        # idx += 2
        # x = F.relu(x, inplace = True)
        w, b = vars[idx], vars[idx + 1]
        coarse = F.linear(x, w, b)
        # point_feat = coarse.unsqueeze(2).expand(-1, -1, self.grid_size ** 2, -1)             # (B, num_coarse, S, 3)
        # point_feat = point_feat.reshape(-1, self.num_dense, 3).transpose(2, 1)               # (B, 3, num_fine)

        # seed = self.folding_seed.unsqueeze(2).expand(B, -1, self.num_coarse, -1)             # (B, 2, num_coarse, S)
        # seed = seed.reshape(B, -1, self.num_dense)                                           # (B, 2, num_fine)

        # feature_global = feature_global.unsqueeze(2).expand(-1, -1, self.num_dense)          # (B, 1024, num_fine)
        # feat = torch.cat([feature_global, seed, point_feat], dim=1)                          # (B, 1024+2+3, num_fine)
    
        # fine = self.final_conv(feat) + point_feat                                            # (B, 3, num_fine), fine point cloud

        return coarse.contiguous()
        
    def parameters(self):
        """
        override this function since initial parameters will return with a generator.
        :return:
        """
        return self.vars

    def set_parameters(self, vars):
        """
        override this function since initial parameters will return with a generator.
        :return:
        """
        self.vars = vars
